using System.Collections.Concurrent;
using TaskServer.ApiServer.Routes.Report.Models;
using static Logging.CCSSLogger;
using TaskStatus = TaskServer.ApiServer.Routes.Report.Models.TaskStatus;

namespace TaskServer.ApiServer.Routes.Report;

/// <summary>
/// 任务状态管理器
/// 负责管理CSV导出任务的状态信息
/// </summary>
public static class TaskStatusManager
{
    /// <summary>
    /// 任务状态存储 - 线程安全
    /// </summary>
    private static readonly ConcurrentDictionary<string, TaskStatus> _taskStatuses = new();
    
    /// <summary>
    /// 最大任务数量限制
    /// </summary>
    private const int MaxTaskCount = 1000;
    
    /// <summary>
    /// 任务过期时间（小时）
    /// </summary>
    private const int TaskExpirationHours = 24;
    
    /// <summary>
    /// 清理定时器
    /// </summary>
    private static readonly Timer _cleanupTimer;
    
    /// <summary>
    /// 静态构造函数，初始化清理定时器
    /// </summary>
    static TaskStatusManager()
    {
        // 每小时执行一次清理
        _cleanupTimer = new Timer(CleanupExpiredTasks, null, TimeSpan.FromHours(1), TimeSpan.FromHours(1));
    }
    
    /// <summary>
    /// 创建新任务
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="message">初始消息</param>
    /// <returns>创建的任务状态</returns>
    public static TaskStatus CreateTask(string taskId, string message = "任务已启动")
    {
        var taskStatus = new TaskStatus
        {
            TaskId = taskId,
            Status = TaskStatusEnum.Started,
            Progress = 0,
            Message = message,
            CreatedTime = DateTime.Now,
            UpdatedTime = DateTime.Now
        };
        
        // 检查任务数量限制
        if (_taskStatuses.Count >= MaxTaskCount)
        {
            CleanupExpiredTasks(null);
        }
        
        _taskStatuses.TryAdd(taskId, taskStatus);
        Logger.Info($"创建CSV导出任务: {taskId}");
        
        return taskStatus;
    }
    
    /// <summary>
    /// 更新任务状态
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="status">新状态</param>
    /// <param name="progress">进度</param>
    /// <param name="message">消息</param>
    /// <param name="errorMessage">错误消息</param>
    public static void UpdateStatus(string taskId, TaskStatusEnum status, int progress = -1, 
        string? message = null, string? errorMessage = null)
    {
        if (_taskStatuses.TryGetValue(taskId, out var taskStatus))
        {
            taskStatus.Status = status;
            taskStatus.UpdatedTime = DateTime.Now;
            
            if (progress >= 0)
                taskStatus.Progress = Math.Min(100, Math.Max(0, progress));
            
            if (!string.IsNullOrEmpty(message))
                taskStatus.Message = message;
            
            if (!string.IsNullOrEmpty(errorMessage))
                taskStatus.ErrorMessage = errorMessage;
            
            Logger.Info($"更新任务状态: {taskId}, 状态: {status}, 进度: {taskStatus.Progress}%");
        }
    }
    
    /// <summary>
    /// 更新任务进度
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="progress">进度百分比</param>
    /// <param name="completedFiles">已完成文件数</param>
    /// <param name="totalFiles">总文件数</param>
    /// <param name="processedRows">已处理行数</param>
    /// <param name="totalRows">总行数</param>
    public static void UpdateProgress(string taskId, int progress, int completedFiles = 0, 
        int totalFiles = 0, long processedRows = 0, long totalRows = 0)
    {
        if (_taskStatuses.TryGetValue(taskId, out var taskStatus))
        {
            taskStatus.Progress = Math.Min(100, Math.Max(0, progress));
            taskStatus.CompletedFiles = completedFiles;
            taskStatus.TotalFiles = totalFiles;
            taskStatus.ProcessedRows = processedRows;
            taskStatus.TotalRows = totalRows;
            taskStatus.UpdatedTime = DateTime.Now;
            
            // 如果进度大于0，状态改为运行中
            if (progress > 0 && taskStatus.Status == TaskStatusEnum.Started)
            {
                taskStatus.Status = TaskStatusEnum.Running;
            }
            
            Logger.Debug($"更新任务进度: {taskId}, 进度: {progress}%, 文件: {completedFiles}/{totalFiles}");
        }
    }
    
    /// <summary>
    /// 获取任务状态
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>任务状态，如果不存在返回null</returns>
    public static TaskStatus? GetStatus(string taskId)
    {
        _taskStatuses.TryGetValue(taskId, out var taskStatus);
        return taskStatus;
    }
    
    /// <summary>
    /// 获取所有任务状态
    /// </summary>
    /// <returns>所有任务状态列表</returns>
    public static List<TaskStatus> GetAllTasks()
    {
        return _taskStatuses.Values.OrderByDescending(t => t.CreatedTime).ToList();
    }
    
    /// <summary>
    /// 删除任务
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>是否删除成功</returns>
    public static bool RemoveTask(string taskId)
    {
        var removed = _taskStatuses.TryRemove(taskId, out _);
        if (removed)
        {
            Logger.Info($"删除任务: {taskId}");
        }
        return removed;
    }
    
    /// <summary>
    /// 清理过期任务
    /// </summary>
    /// <param name="state">定时器状态参数</param>
    private static void CleanupExpiredTasks(object? state)
    {
        var expiredTime = DateTime.Now.AddHours(-TaskExpirationHours);
        var expiredTasks = _taskStatuses.Values
            .Where(t => t.UpdatedTime < expiredTime || 
                       (t.Status == TaskStatusEnum.Failed && t.UpdatedTime < DateTime.Now.AddHours(-1)))
            .Select(t => t.TaskId)
            .ToList();
        
        foreach (var taskId in expiredTasks)
        {
            _taskStatuses.TryRemove(taskId, out _);
        }
        
        if (expiredTasks.Count > 0)
        {
            Logger.Info($"清理过期任务: {expiredTasks.Count} 个");
        }
    }
    
    /// <summary>
    /// 获取当前任务统计信息
    /// </summary>
    /// <returns>任务统计信息</returns>
    public static object GetStatistics()
    {
        var tasks = _taskStatuses.Values.ToList();
        return new
        {
            TotalTasks = tasks.Count,
            StartedTasks = tasks.Count(t => t.Status == TaskStatusEnum.Started),
            RunningTasks = tasks.Count(t => t.Status == TaskStatusEnum.Running),
            CompletedTasks = tasks.Count(t => t.Status == TaskStatusEnum.Completed),
            FailedTasks = tasks.Count(t => t.Status == TaskStatusEnum.Failed)
        };
    }
}
