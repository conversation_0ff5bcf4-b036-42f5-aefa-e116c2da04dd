# 方案2细化实施方案

## 文件变更清单

### 新增文件
1. `TaskServer/ApiServer/Routes/Report/TaskStatusManager.cs` - 任务状态管理器
2. `TaskServer/ApiServer/Routes/Report/Models/TaskStatus.cs` - 任务状态数据模型

### 修改文件
1. `TaskServer/ApiServer/Routes/Report/Routes.cs` - 添加异步导出和状态查询接口
2. `TaskServer/ApiServer/Routes/Report/Service.cs` - 实现异步导出逻辑
3. `clj-backend/src/clj/clj_backend/modules/result_report/service.clj` - 添加异步导出服务
4. `clj-backend/src/clj/clj_backend/modules/result_report/routes.clj` - 添加新路由

## 实施阶段详细规划

### 阶段一：TaskServer异步化改造
**预计时间**: 1天
**负责模块**: C# TaskServer

#### 1.1 创建任务状态数据模型
**文件**: `TaskServer/ApiServer/Routes/Report/Models/TaskStatus.cs`
- 定义TaskStatus类
- 包含任务ID、状态、进度、时间等字段
- 支持序列化/反序列化

#### 1.2 实现任务状态管理器
**文件**: `TaskServer/ApiServer/Routes/Report/TaskStatusManager.cs`
- 内存中存储任务状态（ConcurrentDictionary）
- 提供创建、更新、查询、清理方法
- 线程安全的状态管理
- 自动清理过期任务

#### 1.3 修改导出接口
**文件**: `TaskServer/ApiServer/Routes/Report/Routes.cs`
- 修改现有`ExportCsv`方法为异步模式
- 新增`GetCsvExportStatus`状态查询接口
- 立即返回任务ID，不等待导出完成

#### 1.4 实现异步导出服务
**文件**: `TaskServer/ApiServer/Routes/Report/Service.cs`
- 新增`CsvExportAsync`方法
- 在原有`CsvExport`基础上添加进度回调
- 异常处理和状态更新
- 基于文件数量的进度计算

### 阶段二：Clojure后端适配
**预计时间**: 0.5天
**负责模块**: Clojure后端

#### 2.1 修改导出服务
**文件**: `clj-backend/src/clj/clj_backend/modules/result_report/service.clj`
- 新增`export-csv-async`函数
- 新增`get-csv-export-status`函数
- 调用TaskServer的异步接口
- 保持原有同步接口兼容性

#### 2.2 添加新路由
**文件**: `clj-backend/src/clj/clj_backend/modules/result_report/routes.clj`
- 添加`POST /export-csv-async`路由
- 添加`GET /export-csv-status/:task-id`路由
- 参数验证和错误处理

### 阶段三：集成测试和优化
**预计时间**: 0.5天
**负责模块**: 全栈测试

#### 3.1 单元测试
- TaskStatusManager功能测试
- 异步导出逻辑测试
- 状态查询接口测试

#### 3.2 集成测试
- 端到端导出流程测试
- 并发导出测试
- 异常场景测试

#### 3.3 性能优化
- 内存使用优化
- 任务清理策略调整
- 进度计算精度优化

## 技术实现细节

### 任务状态管理
```csharp
public enum TaskStatusEnum
{
    Started,    // 已启动
    Running,    // 执行中
    Completed,  // 已完成
    Failed      // 失败
}

public class TaskStatus
{
    public string TaskId { get; set; }
    public TaskStatusEnum Status { get; set; }
    public int Progress { get; set; } // 0-100
    public string Message { get; set; }
    public DateTime CreatedTime { get; set; }
    public DateTime UpdatedTime { get; set; }
    public int TotalFiles { get; set; }
    public int CompletedFiles { get; set; }
    public string ErrorMessage { get; set; }
}
```

### 进度计算逻辑
```csharp
private void UpdateProgress(string taskId, int completedFiles, int totalFiles)
{
    var progress = totalFiles > 0 ? (completedFiles * 100) / totalFiles : 0;
    TaskStatusManager.UpdateProgress(taskId, progress, completedFiles, totalFiles);
}
```

### API接口设计
```csharp
// 异步导出接口
POST /export/csv
Request: ExportCsvParams
Response: { "taskId": "guid", "status": "started", "message": "任务已启动" }

// 状态查询接口  
GET /export/csv/status/{taskId}
Response: TaskStatus对象
```

### Clojure接口适配
```clojure
;; 异步导出
(POST "/export-csv-async" request
  (response (service/export-csv-async conn (:body request))))

;; 状态查询
(GET "/export-csv-status/:task-id" [task-id]
  (response (service/get-csv-export-status conn {:task_id task-id})))
```

## 风险控制措施

### 内存管理
- 设置任务状态最大存储数量（默认1000个）
- 自动清理超过24小时的完成任务
- 定期清理失败任务（保留1小时）

### 并发控制
- 使用ConcurrentDictionary确保线程安全
- 限制同时执行的导出任务数量（默认3个）
- 超出限制的任务进入等待队列

### 异常处理
- 完整的try-catch异常捕获
- 详细的错误信息记录
- 任务失败时的状态更新

### 兼容性保证
- 保留原有同步导出接口
- 新接口使用不同的路由路径
- 渐进式迁移策略

## 测试计划

### 功能测试
1. 异步导出接口正常流程测试
2. 状态查询接口测试
3. 任务状态变更测试
4. 错误场景处理测试

### 性能测试
1. 单个大文件导出测试（100万行）
2. 多个小文件并发导出测试
3. 内存使用情况监控
4. 长时间运行稳定性测试

### 兼容性测试
1. 原有同步接口功能验证
2. 新旧接口并存测试
3. 不同参数格式兼容性测试

## 部署计划

### 开发环境部署
1. 代码提交到开发分支
2. 自动化构建和部署
3. 开发环境功能验证

### 测试环境部署
1. 合并到测试分支
2. 完整功能测试
3. 性能测试和压力测试

### 生产环境部署
1. 代码审查和批准
2. 生产环境部署
3. 监控和日志配置
4. 回滚方案准备

## 监控和维护

### 关键指标监控
- 任务创建成功率
- 任务完成成功率
- 平均导出时间
- 内存使用情况
- API响应时间

### 日志记录
- 任务创建和状态变更日志
- 异常和错误详细日志
- 性能指标日志

### 维护计划
- 定期清理过期任务状态
- 监控内存使用情况
- 性能指标分析和优化

## 成功验收标准

### 功能验收
- [ ] 异步导出接口响应时间 < 1秒
- [ ] 状态查询接口响应时间 < 500ms
- [ ] 导出任务成功率 > 95%
- [ ] 进度信息准确性 > 90%

### 性能验收
- [ ] 500个CSV文件导出任务正常执行
- [ ] 系统内存增长 < 100MB
- [ ] 支持3个并发导出任务
- [ ] 24小时连续运行稳定

### 用户体验验收
- [ ] 用户发起导出后立即得到响应
- [ ] 用户可以查询导出进度
- [ ] 导出过程中可以进行其他操作
- [ ] 导出完成或失败有明确状态提示
