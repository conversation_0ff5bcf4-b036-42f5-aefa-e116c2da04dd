# CSV异步导出前端适配方案

## 现状分析

### 当前CSV导出实现
通过代码分析发现，当前CSV导出主要在以下位置：

**主要导出入口**：
- `pages/layout/sample/index.js` - 试样管理页面的CSV导出功能
- 调用 `getExportCSV()` API，同步等待导出完成

**当前实现流程**：
```javascript
const onCSV = async (menuData) => {
    if (menuData?.code) {
        try {
            await getExportCSV({
                sample_code: menuData?.code
            })
            message.success(t('导出csv成功'))
        } catch (err) {
            console.error(err)
        }
    }
}
```

**问题**：
- 同步等待导出完成，用户需要等待8小时
- 没有进度显示
- 导出过程中无法进行其他操作
- 用户体验差

## 前端适配方案

### 方案概述
将现有的同步CSV导出改为异步模式，参考Excel导出的进度条实现，提供：
1. 异步导出启动
2. 实时进度显示
3. 任务状态查询
4. 用户友好的交互体验

### 技术实现

#### 1. 新增API服务函数
**文件**: `react-electron/src/utils/services.js`

```javascript
// 异步导出CSV
export const getExportCSVAsync = (data) => {
    return templateAxios({
        url: API_CONFIG.report_export_csv_async.url,
        method: METHOD_TYPE.POST,
        data
    })
}

// 查询CSV导出状态
export const getCsvExportStatus = (data) => {
    return templateAxios({
        url: API_CONFIG.report_export_csv_status.url,
        method: METHOD_TYPE.POST,
        data
    })
}
```

#### 2. 新增API配置
**文件**: `react-electron/src/utils/serviceConstants.js`

```javascript
export const API_CONFIG = {
    // ... 现有配置
    report_export_csv_async: {
        url: '/report/export/csv/async',
        post: '异步导出csv'
    },
    report_export_csv_status: {
        url: '/report/export/csv/status',
        post: '查询CSV导出状态'
    },
    // ... 其他配置
}
```

#### 3. 修改试样管理页面的CSV导出功能
**文件**: `react-electron/src/pages/layout/sample/index.js`

**核心改动**：
```javascript
const onCSV = async (menuData) => {
    if (menuData?.code) {
        let modal
        try {
            // 启动异步导出
            const exportResult = await getExportCSVAsync({
                sample_code: menuData?.code
            })
            
            const taskId = exportResult?.taskId
            if (!taskId) {
                message.error(t('启动导出任务失败'))
                return
            }
            
            // 显示进度模态框
            modal = Modal.info({
                title: t('导出CSV'),
                content: (
                    <div>
                        <p>{t('正在导出CSV文件...')}</p>
                        <Progress percent={0} status="active" />
                        <p style={{ fontSize: '12px', color: '#666', marginTop: '8px' }}>
                            任务ID: {taskId}
                        </p>
                    </div>
                ),
                okButtonProps: { style: { display: 'none' } },
                cancelButtonProps: { style: { display: 'none' } },
                maskClosable: false
            })
            
            // 轮询查询进度
            const pollProgress = async () => {
                try {
                    const statusResult = await getCsvExportStatus({
                        task_id: taskId
                    })
                    
                    const { status, progress, message: statusMessage, 
                           completedFiles, totalFiles, processedRows, totalRows } = statusResult
                    
                    // 更新进度显示
                    const progressText = totalFiles > 0 
                        ? `${t('正在导出CSV文件...')} (${completedFiles}/${totalFiles})`
                        : t('正在导出CSV文件...')
                    
                    const detailText = totalRows > 0 
                        ? `已处理 ${processedRows.toLocaleString()} / ${totalRows.toLocaleString()} 行数据`
                        : statusMessage || ''
                    
                    modal?.update({
                        content: (
                            <div>
                                <p>{progressText}</p>
                                <Progress percent={progress} status="active" />
                                {detailText && (
                                    <p style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                                        {detailText}
                                    </p>
                                )}
                                <p style={{ fontSize: '12px', color: '#999', marginTop: '8px' }}>
                                    任务ID: {taskId}
                                </p>
                            </div>
                        )
                    })
                    
                    if (status === 'Completed') {
                        // 导出完成
                        modal?.destroy()
                        message.success(t('导出CSV成功'))
                        return
                    } else if (status === 'Failed') {
                        // 导出失败
                        modal?.destroy()
                        message.error(t('导出CSV失败：') + (statusResult.errorMessage || '未知错误'))
                        return
                    }
                    
                    // 继续轮询
                    setTimeout(pollProgress, 2000) // 每2秒查询一次
                } catch (error) {
                    console.error('查询导出状态失败:', error)
                    // 继续轮询，避免网络临时问题
                    setTimeout(pollProgress, 5000) // 出错时5秒后重试
                }
            }
            
            // 开始轮询
            setTimeout(pollProgress, 1000) // 1秒后开始第一次查询
            
        } catch (err) {
            modal?.destroy()
            console.error(err)
            message.error(t('启动CSV导出失败：') + (err.message || '未知错误'))
        }
    }
}
```

#### 4. 创建CSV导出进度组件（可选）
**文件**: `react-electron/src/components/CsvExportProgress/index.js`

```javascript
import React, { useState, useEffect } from 'react'
import { Modal, Progress, Button } from 'antd'
import { useTranslation } from 'react-i18next'
import { getCsvExportStatus } from '@/utils/services'

const CsvExportProgress = ({ taskId, onComplete, onError, onCancel }) => {
    const { t } = useTranslation()
    const [progress, setProgress] = useState(0)
    const [status, setStatus] = useState('Running')
    const [details, setDetails] = useState({})
    const [polling, setPolling] = useState(true)

    useEffect(() => {
        if (!taskId || !polling) return

        const pollProgress = async () => {
            try {
                const result = await getCsvExportStatus({ task_id: taskId })
                setProgress(result.progress || 0)
                setStatus(result.status)
                setDetails(result)

                if (result.status === 'Completed') {
                    setPolling(false)
                    onComplete?.(result)
                } else if (result.status === 'Failed') {
                    setPolling(false)
                    onError?.(result)
                }
            } catch (error) {
                console.error('查询进度失败:', error)
            }
        }

        const timer = setInterval(pollProgress, 2000)
        pollProgress() // 立即执行一次

        return () => clearInterval(timer)
    }, [taskId, polling, onComplete, onError])

    const handleCancel = () => {
        setPolling(false)
        onCancel?.()
    }

    const progressText = details.totalFiles > 0 
        ? `${t('正在导出CSV文件...')} (${details.completedFiles}/${details.totalFiles})`
        : t('正在导出CSV文件...')

    const detailText = details.totalRows > 0 
        ? `已处理 ${details.processedRows?.toLocaleString()} / ${details.totalRows?.toLocaleString()} 行数据`
        : details.message || ''

    return (
        <Modal
            title={t('导出CSV')}
            open={true}
            footer={[
                <Button key="cancel" onClick={handleCancel}>
                    {t('后台运行')}
                </Button>
            ]}
            closable={false}
            maskClosable={false}
        >
            <div>
                <p>{progressText}</p>
                <Progress percent={progress} status={status === 'Failed' ? 'exception' : 'active'} />
                {detailText && (
                    <p style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                        {detailText}
                    </p>
                )}
                <p style={{ fontSize: '12px', color: '#999', marginTop: '8px' }}>
                    任务ID: {taskId}
                </p>
            </div>
        </Modal>
    )
}

export default CsvExportProgress
```

## 实施计划

### 阶段一：API层适配（0.5天）
1. 在 `serviceConstants.js` 中添加新的API配置
2. 在 `services.js` 中添加异步导出和状态查询函数
3. 测试API调用是否正常

### 阶段二：UI层改造（1天）
1. 修改 `sample/index.js` 中的 `onCSV` 函数
2. 实现进度显示和轮询逻辑
3. 添加错误处理和用户提示

### 阶段三：用户体验优化（0.5天）
1. 优化进度显示文案和样式
2. 添加任务ID显示，方便用户跟踪
3. 支持后台运行模式
4. 完善错误处理和重试机制

### 阶段四：测试验证（0.5天）
1. 功能测试：验证异步导出流程
2. 用户体验测试：验证进度显示和交互
3. 异常测试：验证错误处理和恢复

## 用户体验改进

### 交互流程优化
1. **即时反馈**：点击导出后立即显示进度框
2. **详细进度**：显示文件数、行数等详细进度信息
3. **任务跟踪**：显示任务ID，用户可以记录和查询
4. **后台运行**：允许用户关闭进度框，任务继续执行
5. **状态通知**：完成或失败时给出明确提示

### 错误处理改进
1. **网络重试**：查询状态失败时自动重试
2. **详细错误**：显示具体的错误信息
3. **任务恢复**：支持通过任务ID恢复进度查询

## 兼容性考虑

### 渐进式升级
1. **保留原接口**：原有同步导出接口继续可用
2. **功能开关**：可以通过配置控制使用新旧接口
3. **平滑迁移**：用户可以逐步适应新的交互方式

### 降级方案
1. **接口降级**：异步接口不可用时自动使用同步接口
2. **进度降级**：无法获取进度时显示简单的加载状态
3. **功能降级**：保持基本的导出功能可用

## 成功指标

### 用户体验指标
- 导出启动响应时间 < 1秒
- 进度更新频率 2-5秒
- 用户满意度提升

### 技术指标
- 异步导出成功率 > 95%
- 进度查询成功率 > 98%
- 前端无内存泄漏

### 业务指标
- 用户导出操作完成率提升
- 用户投诉和支持请求减少
- 系统整体稳定性提升
