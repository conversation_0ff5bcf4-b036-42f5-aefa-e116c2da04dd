# 方案1实施计划：基于现有任务系统的异步导出

## 项目概述

**目标**: 将CSV导出改造为异步任务，解决前端等待8小时的问题
**预计工期**: 12-16个工作日
**技术风险**: 低
**实施优先级**: 高

## 详细实施计划

### 第一阶段：创建CSV导出子任务类 (3-4天)

#### Day 1-2: 基础框架搭建
**任务清单:**
- [ ] 创建 `SubTaskCsvExport.cs` 文件
- [ ] 实现 `ISubTask` 接口的基本方法
- [ ] 添加任务状态管理属性
- [ ] 实现基础的消息总线集成

**关键代码结构:**
```csharp
public class SubTaskCsvExport : ISubTask
{
    // 基础属性
    private string? _processID;
    private string? _subtaskID; 
    private string? _className;
    private TaskStatus _taskState = TaskStatus.READY;
    
    // 导出相关属性
    private ExportCsvParams _exportParams;
    private int _totalFiles = 0;
    private int _completedFiles = 0;
    
    // 实现接口方法
    public bool Run(SubTaskCmdParams Params) { }
    public bool Finish(SubTaskCmdParams Params) { }
    public bool Abort(SubTaskCmdParams Params) { }
    public bool Pause(SubTaskCmdParams Params) { }
    public bool Resume(SubTaskCmdParams Params) { }
}
```

#### Day 3-4: 核心导出逻辑
**任务清单:**
- [ ] 实现CSV文件生成逻辑
- [ ] 添加进度计算和更新机制
- [ ] 实现错误处理和重试机制
- [ ] 添加日志记录功能

**验收标准:**
- 子任务能够正确启动和结束
- 能够生成CSV文件
- 进度信息能够正确计算
- 异常情况能够正确处理

### 第二阶段：集成到任务调度系统 (2-3天)

#### Day 5-6: 后端接口改造
**任务清单:**
- [ ] 修改 `export-csv` 函数为异步版本
- [ ] 创建任务上下文生成函数
- [ ] 添加任务ID生成逻辑
- [ ] 实现任务启动接口

**关键修改文件:**
- `clj-backend/src/clj/clj_backend/modules/result_report/service.clj`
- `clj-backend/src/clj/clj_backend/modules/result_report/routes.clj`

#### Day 7: 子任务注册和配置
**任务清单:**
- [ ] 在SubTasks系统中注册新的子任务类型
- [ ] 配置任务管理器支持CSV导出任务
- [ ] 测试任务创建和调度流程

**验收标准:**
- 前端调用接口能够立即返回任务ID
- 任务管理器能够正确创建和管理CSV导出任务
- 任务状态能够正确流转

### 第三阶段：实现进度查询接口 (2天)

#### Day 8: 后端进度查询
**任务清单:**
- [ ] 实现任务状态查询函数
- [ ] 添加进度信息获取逻辑
- [ ] 创建REST API接口
- [ ] 实现任务列表查询功能

**新增接口:**
```clojure
;; 查询单个任务进度
["/export/csv/progress"
 {:get {:summary "查询CSV导出进度"
        :parameters {:query {:task_id string?}}
        :handler get-csv-export-progress}}]

;; 查询任务列表
["/export/csv/tasks"
 {:get {:summary "查询CSV导出任务列表"
        :handler get-csv-export-tasks}}]
```

#### Day 9: 数据持久化
**任务清单:**
- [ ] 设计任务状态数据库表
- [ ] 实现任务状态的持久化
- [ ] 添加任务历史记录功能
- [ ] 实现任务清理机制

**验收标准:**
- 能够正确查询任务进度和状态
- 任务信息能够持久化存储
- 支持任务历史记录查询

### 第四阶段：前端界面改造 (3-4天)

#### Day 10-11: 导出流程改造
**任务清单:**
- [ ] 修改现有导出按钮的处理逻辑
- [ ] 实现异步导出调用
- [ ] 添加任务启动成功提示
- [ ] 创建进度查询服务函数

**关键修改文件:**
- `react-electron/src/utils/services.js`
- `react-electron/src/pages/resultReport/index.js`

#### Day 12-13: 进度显示界面
**任务清单:**
- [ ] 创建进度显示模态框组件
- [ ] 实现进度条和状态显示
- [ ] 添加任务控制按钮（暂停、恢复、取消）
- [ ] 实现进度轮询机制

**新增组件:**
```javascript
// 进度显示组件
const CsvExportProgressModal = ({ taskId, visible, onClose }) => {
    // 进度状态管理
    // 轮询逻辑
    // 任务控制功能
};

// 任务管理组件
const CsvExportTaskManager = () => {
    // 任务列表显示
    // 任务状态管理
    // 历史任务查询
};
```

#### Day 14: 用户体验优化
**任务清单:**
- [ ] 添加任务完成通知
- [ ] 实现文件下载链接
- [ ] 优化错误提示信息
- [ ] 添加帮助文档和说明

**验收标准:**
- 用户能够启动异步导出任务
- 能够实时查看导出进度
- 支持任务的基本控制操作
- 用户体验流畅自然

### 第五阶段：测试和优化 (2-3天)

#### Day 15: 功能测试
**测试清单:**
- [ ] 单个文件导出测试
- [ ] 多个文件导出测试
- [ ] 大数据量导出测试
- [ ] 异常情况处理测试
- [ ] 并发任务测试

#### Day 16: 性能优化
**优化清单:**
- [ ] 数据库查询性能优化
- [ ] 内存使用优化
- [ ] 进度更新频率调优
- [ ] 错误处理机制完善

**验收标准:**
- 所有功能测试通过
- 性能指标达到预期
- 系统稳定性良好

## 关键里程碑

| 里程碑 | 完成时间 | 验收标准 |
|--------|----------|----------|
| 子任务类创建完成 | Day 4 | 能够独立运行CSV导出 |
| 任务系统集成完成 | Day 7 | 前端能够启动异步任务 |
| 进度查询功能完成 | Day 9 | 能够查询任务状态和进度 |
| 前端界面改造完成 | Day 13 | 完整的用户交互流程 |
| 测试和优化完成 | Day 16 | 系统稳定可用 |

## 风险控制措施

### 技术风险
1. **任务状态同步问题**
   - 风险：状态更新延迟或丢失
   - 措施：增加状态缓存和重试机制

2. **大数据量处理**
   - 风险：内存溢出或性能下降
   - 措施：实现流式处理和分批处理

3. **并发任务冲突**
   - 风险：多个任务同时运行导致资源竞争
   - 措施：添加任务队列和资源管理

### 进度风险
1. **开发进度延迟**
   - 风险：复杂度超出预期
   - 措施：每日进度检查，及时调整计划

2. **测试时间不足**
   - 风险：测试不充分导致问题遗留
   - 措施：并行开发和测试，提前准备测试用例

## 资源需求

### 人力资源
- **后端开发**: 1人，负责Clojure和C#代码开发
- **前端开发**: 1人，负责React界面改造
- **测试人员**: 1人，负责功能和性能测试

### 技术资源
- **开发环境**: 现有开发环境即可
- **测试数据**: 准备大量测试数据用于性能测试
- **监控工具**: 利用现有日志和监控系统

## 成功标准

### 功能标准
- [x] 前端调用导出接口立即返回任务ID
- [x] 支持实时查询导出进度
- [x] 支持任务暂停、恢复、取消
- [x] 导出结果正确完整

### 性能标准
- [x] 接口响应时间 < 2秒
- [x] 进度查询响应时间 < 1秒
- [x] 任务成功率 > 99%
- [x] 支持3个并发导出任务

### 用户体验标准
- [x] 操作流程简单直观
- [x] 进度信息清晰准确
- [x] 错误提示友好明确
- [x] 任务管理功能完善
