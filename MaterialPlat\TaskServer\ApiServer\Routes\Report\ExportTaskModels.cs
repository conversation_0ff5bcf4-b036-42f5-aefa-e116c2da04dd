using System.Text.Json;

namespace TaskServer.ApiServer.Routes.Report;

/// <summary>
/// 导出任务状态枚举
/// </summary>
public enum ExportTaskStatus
{
    Pending,    // 等待执行
    Running,    // 正在执行
    Completed,  // 已完成
    Failed,     // 执行失败
    Cancelled   // 已取消
}

/// <summary>
/// 导出任务类型枚举
/// </summary>
public enum ExportTaskType
{
    Csv,
    Excel,
    Pdf
}

/// <summary>
/// 导出任务实体
/// </summary>
public class ExportTask
{
    public string Id { get; set; } = string.Empty;
    public ExportTaskType TaskType { get; set; }
    public ExportTaskStatus Status { get; set; } = ExportTaskStatus.Pending;
    public int Progress { get; set; } = 0;
    public int TotalFiles { get; set; } = 0;
    public int CompletedFiles { get; set; } = 0;
    public string ExportParams { get; set; } = string.Empty;
    public string ResultFiles { get; set; } = string.Empty;
    public string ResultFilePath { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
    public DateTime CreatedTime { get; set; } = DateTime.Now;
    public DateTime? StartedTime { get; set; }
    public DateTime? CompletedTime { get; set; }
    public int? CreatedUserId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public string SampleCodes { get; set; } = string.Empty;
    public bool DeleteFlag { get; set; } = false;
}

/// <summary>
/// 启动异步导出请求参数
/// </summary>
public record StartAsyncExportRequest(
    ExportCsvParams ExportParams,
    int? UserId = null
);

/// <summary>
/// 异步导出响应
/// </summary>
public record AsyncExportResponse(
    string TaskId,
    ExportTaskStatus Status,
    string Message = "Task created successfully"
);

/// <summary>
/// 任务状态查询响应
/// </summary>
public record ExportTaskStatusResponse(
    string TaskId,
    ExportTaskStatus Status,
    int Progress,
    int TotalFiles,
    int CompletedFiles,
    List<string> ResultFiles,
    string? ErrorMessage,
    DateTime CreatedTime,
    DateTime? StartedTime,
    DateTime? CompletedTime
);

/// <summary>
/// 任务进度更新参数
/// </summary>
public record TaskProgressUpdate(
    string TaskId,
    int Progress,
    int CompletedFiles,
    string? CurrentFile = null
);

/// <summary>
/// 任务完成参数
/// </summary>
public record TaskCompletionUpdate(
    string TaskId,
    List<string> ResultFiles,
    string ResultFilePath
);

/// <summary>
/// 任务失败参数
/// </summary>
public record TaskFailureUpdate(
    string TaskId,
    string ErrorMessage
);
