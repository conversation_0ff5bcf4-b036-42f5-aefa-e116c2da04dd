using System.Text.Json.Serialization;

namespace TaskServer.ApiServer.Routes.Report.Models;

/// <summary>
/// 任务状态枚举
/// </summary>
public enum TaskStatusEnum
{
    /// <summary>
    /// 已启动
    /// </summary>
    Started,
    
    /// <summary>
    /// 执行中
    /// </summary>
    Running,
    
    /// <summary>
    /// 已完成
    /// </summary>
    Completed,
    
    /// <summary>
    /// 失败
    /// </summary>
    Failed
}

/// <summary>
/// 任务状态信息
/// </summary>
public class TaskStatus
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;
    
    /// <summary>
    /// 任务状态
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public TaskStatusEnum Status { get; set; }
    
    /// <summary>
    /// 进度百分比 (0-100)
    /// </summary>
    public int Progress { get; set; }
    
    /// <summary>
    /// 状态消息
    /// </summary>
    public string Message { get; set; } = string.Empty;
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; }
    
    /// <summary>
    /// 总文件数
    /// </summary>
    public int TotalFiles { get; set; }
    
    /// <summary>
    /// 已完成文件数
    /// </summary>
    public int CompletedFiles { get; set; }
    
    /// <summary>
    /// 总行数
    /// </summary>
    public long TotalRows { get; set; }
    
    /// <summary>
    /// 已处理行数
    /// </summary>
    public long ProcessedRows { get; set; }
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 导出文件路径
    /// </summary>
    public string? ExportPath { get; set; }
}

/// <summary>
/// 异步导出响应
/// </summary>
public class AsyncExportResponse
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;
    
    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; } = string.Empty;
    
    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; } = string.Empty;
}
