using CCSS.HWConnector;
using static Logging.CCSSLogger;
using IHardware;
using NetMQ;
using Utils;
using Consts;
using System.Text;
using System.Text.Json;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using System;
using System.Text.Json.Serialization;
using HwSim16;


namespace Events;


public static class EventHandlers
{

    public static Hw.CDataBlock FilterCDataBlockByCount(Hw.CDataBlock original)
    {
        // 创建一个新的CDataBlock对象
        Hw.CDataBlock filtered = new Hw.CDataBlock
        {
            ServoChCount = original.ServoChCount,
            TempChCount = original.TempChCount,
            CreepChCount = original.CreepChCount,
            InCount = original.InCount,
            OutCount = original.OutCount,
            ADCount = original.ADCount,
        };

        // 一个通用函数用于过滤SingleChDatas数组
        Func<Hw.SingleChDatas[], int, Hw.SingleChDatas[]> filterSingleChDatasArray = (originalArray, count) =>
        {
            //Logger.Debug($"cout: {count}");
            int idx = 0;
            Hw.SingleChDatas[] filteredArray = new Hw.SingleChDatas[count];
            for (int i = 0; i < count; i++)
                filteredArray[i] = new Hw.SingleChDatas(0, 0);

            foreach (var singleChDatas in filteredArray)
            {
                try
                {
                    var originData = originalArray[idx];
                    //Logger.Debug($"chdata count:{originData.DataCount}");
                    singleChDatas.DataCount = originData.DataCount;
                    singleChDatas.ChData = originData.ChData.Take(originData.DataCount).ToArray();
                    idx++;
                }
                catch (Exception e)
                {
                    Logger.Error("eventhandler中数组越界:" + idx);
                    Logger.Error("错误信息为：" + e.Message);
                }

            }
            return filteredArray;
        };

        // 根据ServoChCount过滤ServoData
        filtered.ServoData = filterSingleChDatasArray(original.ServoData!, original.ServoChCount);

        // 根据TempChCount过滤TempData
        filtered.TempData = filterSingleChDatasArray(original.TempData!, original.TempChCount);

        // 根据CreepChCount过滤CreepData
        filtered.CreepData = filterSingleChDatasArray(original.CreepData!, original.CreepChCount);

        // 根据InCount和OutCount过滤BitIn和BitOut
        filtered.BitIn = original.BitIn?.Take(original.InCount).ToArray();
        filtered.BitOut = original.BitOut?.Take(original.OutCount).ToArray();

        // 根据ADCount过滤ADData
        if (original.ADData != null && original.ADData.Length > original.ADCount)
        {
            filtered.ADData = original.ADData.Take(original.ADCount).ToArray();
            foreach (var singleAD in filtered.ADData)
            {
                if (singleAD.Sensor != null && singleAD.Sensor.Length > singleAD.DataCount)
                {
                    singleAD.Sensor = singleAD.Sensor.Take(singleAD.DataCount).ToArray();
                    singleAD.MaxSensor = singleAD.MaxSensor?.Take(singleAD.DataCount).ToArray();
                    singleAD.MinSensor = singleAD.MinSensor?.Take(singleAD.DataCount).ToArray();
                    singleAD.Time = singleAD.Time?.Take(singleAD.DataCount).ToArray();
                }
            }
        }
        else
        {
            filtered.ADData = original.ADData;
        }

        return filtered;
    }


    static EventHandlers() =>
        NetMQMsgPublisherSubject.Subscribe(msg => HWDataClient.sendMsg(msg));
    private static readonly Subject<NetMQMessage> NetMQMsgPublisherSubject = new();
    private static void SendMsg(NetMQMessage msg) =>
                NetMQMsgPublisherSubject.OnNext(msg);


    public static int servoDAQRate = 0;

    private static readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
    {
        WriteIndented = false,
    };
    
    public static int DataBlockHandler(int _sender, ref Hw.CDataBlock DataBlock)
    {
        try
        {

            NetMQMessage msg = new();
            // header
            msg.Append(BitConverter.GetBytes((int)HardwareDataMsg.DataBlock));

            // 使用FlatCDataBlock进行高性能序列化，输出二进制数据
            var flatFilteredData = FilterFlatCDataBlockByCount(DataBlock);
            var messagePackBytes = Consts.MessagePackSerializer.Serialize(flatFilteredData);
            msg.Append(messagePackBytes);

            // 使用FlatCDataBlock版本的数据检查
            if (CanSendFlatMsg(flatFilteredData))
                SendMsg(msg);

            // Logger.Info("DoOnDataBlockEvent call back  Timer:" + DataBlock.CreepData[0].DataCount == null ? 0 : DataBlock.CreepData[0].DataCount);
            //if (DataBlock.ServoData[0].ChData[0].Sensor[0] != 0 && DataBlock.ServoData[0].ChData[0].Sensor[1] != 0 && DataBlock.ServoData[0].ChData[0].Sensor[2] != 0)
            //{
            //    Logger.Debug("DoOnDataBlockEvent call back");
            //    Logger.Debug("DoOnDataBlockEvent call back  Timer:" + DataBlock.ServoData[0].ChData[0].Timer);
            //    Logger.Debug("DoOnDataBlockEvent call back  DAQRate:" + servoDAQRate);
            //    Logger.Debug("DoOnDataBlockEvent call back  Move:" + DataBlock.ServoData[0].ChData[0].Sensor[0]);
            //    Logger.Debug("DoOnDataBlockEvent call back  Load:" + DataBlock.ServoData[0].ChData[0].Sensor[1]);
            //    Logger.Debug("DoOnDataBlockEvent call back  Stren:" + DataBlock.ServoData[0].ChData[0].Sensor[2]);
            //}
            return 0;
          }
        catch (Exception ex)
        {
            Logger.Error($"DataBlockHandler: {ex}");
            return 0;
        }
    }

    // 设备状态回调
    // TODO  SubID 这个参数不确定客户的dll中的回调是否封装，有可能收到的全都是 0
    public static int LineEventHandler(int SubId, ref int LineState)
    {
        NetMQMessage msg = new();
        // header
        msg.Append(BitConverter.GetBytes((int)Consts.HardwareDataMsg.Line));
        //sender 其实是设备ID  即subId
        msg.Append(BitConverter.GetBytes(SubId));
        msg.Append(BitConverter.GetBytes(LineState));
        SendMsg(msg);
        Logger.Error($"LineEventHandler call back  SubId：{SubId}  LineState：{LineState}");
        return 0;
    }

    // 设备命令执行完成回调
    public static int PosMsgEventHandler(int _sender, ref Hw.OnPosMsg PosMsg)
    {
        NetMQMessage msg = new();
        msg.Append(BitConverter.GetBytes((int)Consts.HardwareDataMsg.PosMsg));
        msg.Append(BitConverter.GetBytes(PosMsg.HwType));
        msg.Append(BitConverter.GetBytes(PosMsg.DeviceID));
        msg.Append(BitConverter.GetBytes(PosMsg.ADSensorID));
        msg.Append(BitConverter.GetBytes(PosMsg.CMDType));
        msg.Append(BitConverter.GetBytes(PosMsg.SingleReached));
        msg.Append(BitConverter.GetBytes(PosMsg.AllReached));
        msg.Append(BitConverter.GetBytes(PosMsg.Tan));
        SendMsg(msg);
        Logger.Info("PosMsgEventHandler call back");
        Logger.Info("PosMsgEventHandler call back  Consts:" + (int)Consts.HardwareDataMsg.PosMsg);
        Logger.Info("PosMsgEventHandler call back  HwType:" + PosMsg.HwType);
        Logger.Info("PosMsgEventHandler call back  DeviceID:" + PosMsg.DeviceID);
        Logger.Info("PosMsgEventHandler call back  ADSensorID:" + PosMsg.ADSensorID);
        Logger.Info("PosMsgEventHandler call back  CMDType:" + PosMsg.CMDType);
        Logger.Info("PosMsgEventHandler call back  SingleReached:" + PosMsg.SingleReached);
        Logger.Info("PosMsgEventHandler call back  AllReached:" + PosMsg.AllReached);
        Logger.Error("PosMsgEventHandler call back  Tan:" + PosMsg.Tan);
        return 0;
    }

    // 设备系统消息回调
    // 类型有   0：状态信息(0)  1：错误信息(10000)  2.警告(10001)
    public static int SystemMsgEventHandler(int bak, ref Hw.OnSystemMsg SystemMsg)
    {
        // 20250307, 蠕变测试，两(多)个轴同时回调，需要快照一份数据避免mq报错。问题原因：云效XJRS-4854
        int hwType = SystemMsg.HwType;
        int deviceID = SystemMsg.DeviceID;
        int adSensorID = SystemMsg.ADSensorID;
        int msgNumber = (int)SystemMsg.MsgNumber;
        string text = SystemMsg.Text;
        double time = SystemMsg.Time;
        NetMQMessage msg = new();
        // header
        msg.Append(BitConverter.GetBytes((int)Consts.HardwareDataMsg.SystemMsg));
        msg.Append(BitConverter.GetBytes(hwType));
        msg.Append(BitConverter.GetBytes(deviceID));
        msg.Append(BitConverter.GetBytes(adSensorID));
        msg.Append(BitConverter.GetBytes(msgNumber));
        msg.Append(Encoding.UTF8.GetBytes(text));
        msg.Append(time.toBytes());
        SendMsg(msg);
        Logger.Info("SystemMsgEventHandler call back");
        Logger.Info("SystemMsgEventHandler call back Text:" + text);
        return 0;
    }

    // 手控盒信息
    public static int RmcMsgEventHandler(int bak, ref Hw.OnHwMsg HwEvent)
    {
        NetMQMessage msg = new();
        // header
        msg.Append(BitConverter.GetBytes((int)Consts.HardwareDataMsg.HwMsg));
        msg.Append(BitConverter.GetBytes(HwEvent.HwType));
        msg.Append(BitConverter.GetBytes(HwEvent.HwID));
        msg.Append(Encoding.UTF8.GetBytes(HwEvent.BackString));
        SendMsg(msg);
        Logger.Error($"RmcMsgEventHandler call back msg:{bak} HwEventHwtype:{HwEvent.HwType} HwEventHwID:{HwEvent.HwID} HwEventBackString:{HwEvent.BackString}");
        return 0;
    }

    /// <summary>
    /// 判断这条数据是否需要发送,全都是0 则不发送
    /// </summary>
    /// <param name="cDataBlock"></param>
    /// <returns>返回false则不发送数据</returns>
    private static bool CanSendMsg(Hw.CDataBlock cDataBlock)
    {
        //类轴的数据全为0，则不发送
        bool allZero1 = (cDataBlock.ServoData == null || cDataBlock.ServoData!.Length == 0) ? true : cDataBlock.ServoData[0].DataCount == 0;
        bool allZero2 = (cDataBlock.CreepData == null || cDataBlock.CreepData!.Length == 0) ? true : cDataBlock.CreepData[0].DataCount == 0;
        bool allZero3 = (cDataBlock.TempData == null || cDataBlock.TempData!.Length == 0) ? true : cDataBlock.TempData[0].DataCount == 0;

        if (allZero1 && allZero2 && allZero3)
        {
            Logger.Error("所有轴的数据都为0!");
            return false;
        }
        return true;
    }
    static int i = 0;
    /// <summary>
    /// 判断这条FlatCDataBlock数据是否需要发送,全都是0 则不发送
    /// </summary>
    /// <param name="flatDataBlock"></param>
    /// <returns>返回false则不发送数据</returns>
    private static bool CanSendFlatMsg(Consts.FlatCDataBlock flatDataBlock)
    {
        try
        {


            //类轴的数据全为0，则不发送
            bool allZero1 = (flatDataBlock.ServoData == null || flatDataBlock.ServoData!.Length == 0) ? true : flatDataBlock.ServoData[0].DataCount == 0;
            bool allZero2 = (flatDataBlock.CreepData == null || flatDataBlock.CreepData!.Length == 0) ? true : flatDataBlock.CreepData[0].DataCount == 0;
            bool allZero3 = (flatDataBlock.TempData == null || flatDataBlock.TempData!.Length == 0) ? true : flatDataBlock.TempData[0].DataCount == 0;


            if (allZero1 && allZero2 && allZero3)
            {
                Logger.Error("所有轴的数据都为0!");
                return false;
            }
            i++;
            if (i >= 100)
            {
                Logger.Error(flatDataBlock.ToJson());
                Logger.Error($" flatDataBlock.CreepData!.Length：{flatDataBlock.CreepData!.Length} flatDataBlock.TempData!.Length：{flatDataBlock.TempData!.Length}");
                i = 0;
            }
            return true;
        }
        catch (Exception ex)
        {
            Logger.Error($"CreepDataBlockHandler: {ex}");
            return false;
        }
    }

    public static Consts.FlatCDataBlock FilterFlatCDataBlockByCount(Hw.CDataBlock original)
    {
        // 先过滤原始数据
        var filteredOriginal = FilterCDataBlockByCount(original);

        var flatDataBlock = Consts.FlatCDataBlock.FromCDataBlock(new Consts.CDataBlock
        {
            ServoChCount = filteredOriginal.ServoChCount,
            TempChCount = filteredOriginal.TempChCount,
            CreepChCount = filteredOriginal.CreepChCount,
            InCount = filteredOriginal.InCount,
            OutCount = filteredOriginal.OutCount,
            ADCount = filteredOriginal.ADCount,
            ServoData = ConvertToConstsFormat(filteredOriginal.ServoData),
            TempData = ConvertToConstsFormat(filteredOriginal.TempData),
            CreepData = ConvertToConstsFormat(filteredOriginal.CreepData),
            BitIn = filteredOriginal.BitIn,
            BitOut = filteredOriginal.BitOut,
            ADData = ConvertADDataToConstsFormat(filteredOriginal.ADData)
        });
        
        return flatDataBlock;
    }

    private static Consts.SingleChDatas[]? ConvertToConstsFormat(Hw.SingleChDatas[]? hwData)
    {
        if (hwData == null) return null;
        
        return hwData.Select(hw => new Consts.SingleChDatas
        {
            DataCount = hw.DataCount,
            ChData = hw.ChData?.Select(cd => new Consts.CData
            {
                ActiveCtrl = cd.ActiveCtrl,
                Command = cd.Command,
                Feedback = cd.Feedback,
                Output = cd.Output,
                Timer = cd.Timer,
                BlockCycles = cd.BlockCycles,
                Cycles = cd.Cycles,
                BlockLine = cd.BlockLine,
                CmdFrequency = cd.CmdFrequency,
                UpperLimits = cd.UpperLimits,
                UpperSft = cd.UpperSft,
                LowerLimits = cd.LowerLimits,
                LowerSft = cd.LowerSft,
                SensorCount = cd.SensorCount,
                Sensor = cd.Sensor,
                MaxSensor = cd.MaxSensor,
                MinSensor = cd.MinSensor,
                InSignals = cd.InSignals
            }).ToArray()
        }).ToArray();
    }

    private static Consts.SingleADDatas[]? ConvertADDataToConstsFormat(Hw.SingleADDatas[]? hwAdData)
    {
        if (hwAdData == null) return null;
        
        return hwAdData.Select(ad => new Consts.SingleADDatas
        {
            DataCount = ad.DataCount,
            Sensor = ad.Sensor,
            MaxSensor = ad.MaxSensor,
            MinSensor = ad.MinSensor,
            Time = ad.Time
        }).ToArray();
    }

}
