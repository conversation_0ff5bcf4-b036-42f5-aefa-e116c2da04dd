(ns clj-backend.modules.result-report.service
  "导出报表service"
  (:require
   [clj-backend.common.biz-error :refer [errors throw-error]]
   [clj-backend.common.db-utils :as db-utils]
   [clj-backend.common.excel-utils :as excel-utils]
   [clj-backend.common.http-client :refer [post]]
   [clj-backend.common.io-utils :as io-utils :refer [file-separator]]
   [clj-backend.common.template-utils :refer [gen-class-name]]
   [clj-backend.common.utils :as utils]
   [clj-backend.db.core :refer [*db*]]
   [clj-backend.modules.log.log-service :refer [save-log]]
   [clj-backend.modules.result-report.db :as db]
   [clj-backend.modules.round.round-service :refer [run-number]]
   [clj-backend.modules.units.units-db :refer [all-units]]
   [clj-backend.modules.variable.input.input-variable-db
    :refer [get-sample-input-vars]]
   [clj-http.client :as client]
   [clojure.string :as cstr]
   [conman.core :as conman]))

(declare export-confg-redefault)


(def ^:private variable-result-table-name "t_variable_result")
(def ^:private variable-input-table-name "t_variable_input")
(def ^:private variable-signal-table-name "t_variable_signal")
(def ^:private sample-instance-table-name "t_sample_instance")
(def ^:private export-confg-table-name "t_export_config")
(def ^:private sample-parameter-table-name "t_sample_parameter")
(def ^:private sample-variable-input-table-name "t_sample_variable")
(def ^:private units-table-name "t_units")
(def ^:private Results-table-name "Results")
(def ^:private MessagesDAQ-table-name "MessagesDAQ")
(defn- get-data-table-name
  [simple-name]
  (format "data.%s" simple-name))

(def ^:private tab-columns
  ["signal_config"
   "input_config"
   "result_config"
   "statistic_config"
   "pdf_config"])

(defn get-variable-input
  [conn {:keys [codes sample_code]}]
  (let [variable-input (group-by :sample_code
                                 (db-utils/find-list-complex
                                  conn
                                  {:table-name sample-variable-input-table-name
                                   :conds (if sample_code
                                            {:delete_flag 0
                                             :sample_code sample_code}
                                            {:delete_flag 0})
                                   :array-conds {:variable_code codes}}))]
    (reduce-kv
     (fn [m k v]
       (let [input-val (reduce #(let [val (utils/read-json (:variable_val %2))]
                                  (assoc %1 (:variable_code %2) (:value val)))
                               {}
                               v)]
         (assoc m k input-val)))
     {}
     variable-input)))


(defn get-variable-result
  [conn {:keys [codes]}]
  (reduce #(let [message (utils/read-json (:message %2))
                 result-data (reduce (fn [r v]
                                       (assoc r (:code v) (:value v)))
                                     {}
                                     message)]
             (assoc %1 (:sample_instance_code %2) result-data))
          {}
          (db/result-variable-data
           conn
           {:table-name Results-table-name
            :codes codes})))


(defn- get-sample-units
  [conn parameter-id]
  (db/get-sample-instance-units
   conn
   {:table-name1 sample-parameter-table-name
    :table-name2 units-table-name
    :parameter_id parameter-id}))


(defn get-sample-info
  "TODO 目前试样参数不全"
  [conn {:keys [codes]}]
  (reduce #(let [data (utils/read-json (:data %2))
                 r    (reduce (fn [u d]
                                (let [units (get-sample-units conn (:parameter_id d))]
                                  (assoc u (:code units) {:units (:name units)
                                                          :value (:value d)})))
                              {}
                              data)]
             (assoc %1 (:code %2) r))
          {}
          (db/get-sample-instance-by-code
           conn
           {:table-name sample-instance-table-name
            :codes      codes})))


;; -------------------------------------------------------------------------------------------------

(def number_format_type
  {:auto "auto" ;// 自动
   :power "power" ;// 幂数型
   :custom "custom" ;// 客户定义
   :valid "valid" ;// 有效定义
   :rounding "rounding" ;'// 修约区间
   })

(defn convert-unit
  "单位换算"
  ([units param-val dimension-id unit-id]
   (convert-unit units param-val dimension-id unit-id ""))
  ([units param-val  dimension-id unit-id default-unit-id]
   (let [dimension (some #(when (= (:id %) dimension-id) %) units)
         default-unit  (or default-unit-id (:default_unit_id dimension))
         defaul-proportion (or (:proportion (some #(when (= (:id %) default-unit) %) units)) 1)
         target-proportion (or (:proportion (some  #(when (= (:id %) unit-id) %) (:units dimension))) 1)]
     (if (= default-unit unit-id)
       param-val
       (* param-val (/ target-proportion defaul-proportion))))))

(defn result-fractional-digit
  "结果变量格式"
  [format-type data]
  (condp = format-type
    (:power number_format_type) (:exponential_decimal_digits data)
    (:custom number_format_type) (or (:decimal_digits data) 0)
    (:valid number_format_type) (or (:significant_digits data) 3)
    (:rounding number_format_type) (or (:modified_interval data) 1)
    0))

(defn input-fractional-digit
  "输入变量格式"
  [data]
  (let [format (:format data)]
    (condp = (:format-type data)
      (:power number_format_type) (:pointPosition format)
      (:custom number_format_type) (or (:afterPoint format) 0)
      (:valid number_format_type) (or (:significantDigits  format) 3)
      (:rounding number_format_type) {:roundMode (:roundMode format)
                                      :threshold1 (:threshold1 format)
                                      :threshold2 (:threshold2 format)
                                      :roundType1 (:roundType1 format)
                                      :roundType2 (:roundType2 format)
                                      :roundType3 (:roundType3 format)}
      0)))

(defn format-value
  "数字格式化
   e: 科学计数法
   f: 固定小数点
   g: 通用格式"
  [format-type num fractional-digit id]
  (let [new-num (Double. (str num))]
    (if new-num
      (condp = format-type
        (:auto number_format_type) new-num
        (:power number_format_type) (format (str "%." (or fractional-digit 0) "e") new-num)
        (:custom number_format_type) (format (str "%." (or fractional-digit 0) "f") new-num)
        (:valid number_format_type) (format (str "%." (or fractional-digit 3) "g") new-num)
        (:rounding number_format_type) (run-number
                                        {:source_data new-num
                                         :type (:roundMode fractional-digit)
                                         :thres_hold1 (or (:threshold1 fractional-digit) 0)
                                         :thres_hold2 (or (:threshold2 fractional-digit) 0)
                                         :round_type1 (or (:roundType1 fractional-digit) 0)
                                         :round_type2 (or (:roundType2 fractional-digit) 0)
                                         :round_type3 (or (:roundType3 fractional-digit) 0)
                                         :project_id id
                                         :template_id id})
        new-num)
      new-num)))


(defn merge-data [datas]
  (->> datas
       (group-by :code)
       vals
       (mapv (fn [group]
               (merge (first group)
                      {:data (reduce concat (map :data group))})))))


(defn get-variable-results
  "获取-结果数据"
  [conn {:keys [codes project_id] :as query}
   result_variables
   sample-data
   units]
  (let [order       (remove nil? (map :code result_variables))
        result-data (db-utils/find-list-complex
                     conn
                     {:table-name  Results-table-name
                      :array-conds {:sample_instance_code codes}})
        data'
        (map
         (fn [i]
           (let [data   (utils/read-json (:message i))
                 sample (some #(when (= (:code %) (:sample_instance_code i)) %) sample-data)
                 param  {:name (:name sample)
                         :code (:sample_instance_code i)}]
             (if (seq data)
               (assoc param
                      :data (->>
                             data
                             (map (fn [i]
                                    (let [e (some #(when (= (:code %) (:code i)) %) result_variables)]
                                      (when (seq e)
                                        (merge e i)))))
                             (remove nil?)
                             (sort-by
                              #((into {} (map-indexed (fn [i e] [e i]) order)) (:code %)))))
               param)))
         result-data)
        data        (merge-data data')]

    {:format-data
     (map
      (fn [i]
        (assoc i :data
               (map
                (fn [j]
                  (assoc j
                         :value (format-value
                                 (:format_type j)
                                 (convert-unit
                                  units
                                  (if (string? (:value j)) 0 (:value j))
                                  (:dimension_id j)
                                  (:unit_id j))
                                 (result-fractional-digit
                                  (:format_type j)
                                  (:format_info j))
                                 project_id)))  (:data i))))
      data)
     :data data}))

(defn get-sample-daq
  [conn {:keys [codes project_id] :as query}]
  (map
   #(utils/parse-special-key-in-map [:message] %)
   (db-utils/find-list-complex
    conn
    {:table-name (get-data-table-name MessagesDAQ-table-name)
     :select-cols ["sample_instance_code","message","createtime"]
     :array-conds {:sample_instance_code codes}})))

(defn get-sample-data
  [conn {:keys [codes] :as param}]
  (let [samples    (db-utils/find-list-complex
                 conn
                 {:table-name  sample-instance-table-name
                  :select-cols ["id","name","code","color"]
                  :array-conds {:code codes}
                  :not-conds   {:parent_group 0
                                :delete_flag  1}})
        sample-daq (get-sample-daq conn (assoc param :codes (map :code samples)))
        groups     (group-by :sample_instance_code sample-daq)]
    (map (fn [i]
           (assoc i :daq_data (get groups (:code i)))) samples)))

(def statistic-data
  [{:id "1"
    :name "平均值"}
   {:id "2"
    :name "最小值"}
   {:id "3"
    :name "最大值"}
   {:id "4"
    :name "方差值"}])



(defn variance [numbers]
  (let [mu (/ (reduce + numbers) (count numbers))
        squared-diffs (map #(* (- % mu) (- % mu)) numbers)]
    (/ (reduce + squared-diffs) (count squared-diffs))))


(defn calculate
  [id numbers]
  (if (seq numbers)
    (condp = id
      "1"  (/ (reduce + numbers) (count numbers))
      "2"  (apply min numbers)
      "3"  (apply max numbers)
      "4"  (variance numbers)
      0)
    0))

(defn get-unit [units data]
  (let [dimension (some #(when (= (:id %) (:dimension_id data)) %) units)
        unit (some  #(when (= (:id %) (:unit_id data)) %) (:units dimension))]
    (merge data {:dimension_name (:name dimension)
                 :format_info (utils/read-json (:format_info data))
                 :unit_name (:name unit)})))

(defn get-signal-config
  "获取-信号变量"
  [signal_config sample-data units]
  (let [signals  (-> signal_config :signals)
        x' (some #(when (= (:signal_variable_id %) (-> signal_config :signal_config_x)) %) signals)
        y' (some #(when (= (:signal_variable_id %) (-> signal_config :signal_config_y)) %) signals)
        x (get-unit units x')
        y (get-unit units y')]
    (map (fn [i]
           (let [daq_data (:daq_data i)]
             (assoc i
                    :x_config x
                    :x_data (remove nil?
                                    (map
                                     (fn [daq]
                                       (let [data (-> daq  :message :UIParams :VarValues)]
                                         (some #(when (= (:Code %) (:code x))
                                                  (merge %
                                                         {:Value (convert-unit
                                                                  units
                                                                  (:Value %)
                                                                  (:dimension_id x)
                                                                  (:unit_id x))})) data)))
                                     daq_data))
                    :y_config y
                    :y_data (remove nil?
                                    (map
                                     (fn [daq]
                                       (let [data (-> daq  :message :UIParams :VarValues)]
                                         (some #(when (= (:Code %) (:code y))
                                                  (merge %
                                                         {:Value (convert-unit
                                                                  units
                                                                  (:Value %)
                                                                  (:dimension_id y)
                                                                  (:unit_id y))})) data)))
                                     daq_data)))))
         sample-data)))

(defn get-result-data
  "获取-测试结果数据"
  [result_variables results_data]
  (let [result_variables (conj
                          result_variables
                          {:variable_name ""
                           :unit_name ""})]
    [(map :variable_name result_variables)
     (map :unit_name result_variables)
     results_data]))

(defn get-statistic_data
  "获取-统计表格数据"
  [statistic_config codes result_variables results_data units id]
  (let [statistic_variables (conj
                             result_variables
                             {:variable_name "结果文件"
                              :unit_name     (str "n=" (count codes))})
        groups              (->> results_data
                                 (mapcat :data)
                                 (group-by :code))
        statistic_data      (->>
                             statistic-data
                             (filter
                              (fn [i]
                                (some #(= (:id i) %1) statistic_config)))
                             (map
                              (fn [i]
                                (assoc i
                                       :data
                                       (map (fn [j]
                                              {:value (format-value
                                                       (:format_type j)
                                                       (convert-unit
                                                        units
                                                        (calculate (:id i)
                                                                   (map #(if (string? (:value %))
                                                                           0
                                                                           (:value %))
                                                                        (get groups (:code j))))
                                                        (:dimension_id j)
                                                        (:unit_id j))
                                                       (result-fractional-digit
                                                        (:format_type j)
                                                        (:format_info j))
                                                       id)})
                                            result_variables)))))]
    [(map :variable_name statistic_variables)
     (map :unit_name statistic_variables)
     statistic_data]))

#_(defn export-excel-data
    "导出报表"
    [conn {:keys [project_id] :as param}]
    (let [data-config     (db-utils/find-detail-complex
                           conn
                           {:table-name export-confg-table-name
                            :conds      {:delete_flag  0
                                         :default_flag 1
                                         :export_type  (:export_type param)}})
          _               (when-not (seq data-config) (throw-error 4000))
          config          (utils/parse-special-key-in-map
                           (mapv keyword tab-columns)
                           data-config)
          _               (when-not (io-utils/path-is-file? (:export_path config))  (throw-error 4001))
          units           (dimension-units-list conn param)
          input_variables (map (fn [i]
                                 (let [number-format (utils/read-json (:number_tab i))
                                       sample-detail (db-utils/find-detail
                                                      conn
                                                      {:table-name  sample-variable-input-table-name
                                                       :select-cols ["variable_val"]
                                                       :id-name     "variable_code"
                                                       :id          (:code i)})

                                       variable_val (utils/read-json (:variable_val sample-detail))]
                                   {:code  (:code i)
                                    :name  (:name i)
                                    :value (if (= (:variable_type i) "Number")
                                             (format-value
                                              (-> number-format :format :formatType)
                                              (convert-unit
                                               units
                                               (:value variable_val)
                                               (:unitType variable_val)
                                               (:unit variable_val))
                                              (input-fractional-digit  number-format)
                                              project_id)
                                             (:value variable_val))}))
                               (db-utils/find-list-complex
                                conn
                                {:table-name  variable-input-table-name
                                 :array-conds {:id (:input_config config)}
                                 :select-cols ["code","default_val","variable_type","number_tab","name"]})) sample-data      (get-sample-data conn param)
          ;; 获取数据库数据
          result_variables (map
                            #(get-unit units %)
                            (db-utils/find-list-complex
                             conn
                             {:table-name  variable-result-table-name
                              :array-conds {:result_variable_id (:result_config config)}
                              :select-cols ["code","result_variable_id","variable_name","dimension_id","unit_id", "format_type" "format_info"]}))
          ;; 获取历史结果变量数据
          results_data     (get-variable-results conn param result_variables sample-data units)
          result-data      (get-result-data result_variables (:format-data results_data))
          statistic-data   (get-statistic_data (:statistic_config config) (:codes param) result_variables (:data results_data) units project_id)
          sample-data      (get-signal-config (:signal_config config) sample-data units)]
      (export-data-excel
       {:output-file-path (str (:export_path config) file-separator (:export_name config) "." (:file_type config))
        :input-variables  input_variables
        :result-data      result-data
        :statistic-data   statistic-data
        :sample-data      sample-data})))

(defn- try-get-unit&factor
  "获取变量和变量的因数(变量id为空或者无效时返回nil)"
  [units unit-id]
  (when (seq unit-id)
    (let [unit (some (fn [unit] (when (= (:id unit) unit-id) unit)) units)]
      {:code   (:name unit)
       :factor (:proportion unit)})))

(defn- get-formative-inputvars
  "获取格式化的信号变量数据"
  [inputvars units]
  (mapv
   (fn [input-var]
     (let [value-info (utils/read-json (:variable_val input-var))]
       {:name        (:name input-var)
        :value       (:value value-info)
        :unit        (try-get-unit&factor units (:unit input-var))
        :sample_code (:sample_code input-var)}))
   inputvars))

(defn- get-report-sample-insts
  "获取报表试样信息"
  [conn sample-codes inputvar-ids units]
  (let [sample-insts          (db-utils/find-list-complex
                               conn
                               {:table-name  sample-instance-table-name
                                :select-cols ["id" "code" "name"]
                                :array-conds {:code sample-codes}})
        insts-input-variables (-> (when (seq inputvar-ids)
                                    (get-sample-input-vars
                                     conn
                                     {:sample_ids   (mapv :id sample-insts)
                                      :variable_ids inputvar-ids}))
                                  (get-formative-inputvars units))]
    (mapv
     (fn [sample-inst]
       {:name      (:name sample-inst)
        :code      (:code sample-inst)
        :inputVars (filterv #(= (:code sample-inst) (:sample_code %)) insts-input-variables)})
     sample-insts)))

(defn- gen-format-info
  "获取格式化信息"
  [json-str]
  (let [format-info (utils/read-json json-str)]
    (cond
      ;; 幂数型
      (:exponential_decimal_digits format-info)
      {:exponentialDecimalDigits (:exponential_decimal_digits format-info)}

      ;; 自定义
      (:decimal_digits format-info)
      {:DecimalDigits (:decimal_digits format-info)}

      ;; 有效数字
      (:significant_digits format-info)
      {:significantDigits (:significant_digits format-info)}

      ;; 修约
      (every? boolean ((juxt :threshold1 :threshold2 :roundMode
                             :roundType1 :roundType2 :roundType3) format-info))
      {:roundParams {:threshold1 (:threshold1 format-info)
                     :threshold2 (:threshold2 format-info)
                     :roundMode  (:roundMode format-info)
                     :roundType1 (:roundType1 format-info)
                     :roundType2 (:roundType2 format-info)
                     :roundType3 (:roundType3 format-info)}}

      :else {})))

(defn- get-report-result-vars
  "获取报表结果变量"
  [conn result-var-ids units]
  (let [result-vars (when (seq result-var-ids)
                      (db-utils/find-list-complex
                       conn
                       {:table-name  variable-result-table-name
                        :select-cols ["code" "unit_id" "format_info"]
                        :array-conds {:result_variable_id result-var-ids}}))]
    (mapv
     (fn [result-var]
       {:code       (:code result-var)
        :unit       (try-get-unit&factor units (:unit_id result-var))
        :formatInfo (gen-format-info (:format_info result-var))})
     result-vars)))

(defn- get-report-config
  "获取报表设置"
  [conn param]
  (let [db-config (db-utils/find-detail-complex
                   conn
                   {:table-name export-confg-table-name
                    :conds      {:delete_flag  0
                                 :default_flag 1
                                 :export_type  (:export_type param)}})
        _         (when-not (seq db-config) (throw-error 4000))
        config    (utils/parse-special-key-in-map
                   (mapv keyword tab-columns)
                   db-config)]

    (when-not (io-utils/path-is-file? (:export_path config))  (throw-error 4001))
    config))

(defn- taskserver-export
  "调用taskServer接口导出报表"
  [form-body]
  (-> (post "http://localhost:5002/export" form-body)
      (:body)
      (utils/read-json)
      (:Data)
      (as-> exception
          (condp = exception
            "IOException"     (throw-error (:TargetFileBeUsing errors))
            "SqliteException" (throw-error (:ReportSignalVarNonexistent errors))
            "Success"))))

(defn- taskserver-export-csv
  "调用taskServer接口导出csv"
  [form-body]
  (-> (client/post "http://localhost:5002/export/csv"
                   {:form-params  form-body
                    :content-type :json
                    :accept       :json
                    :debug        true})
      (:body)
      (utils/read-json)
      (:Data)
      (as-> exception
          (condp = exception
            "IOException"     (throw-error (:TargetFileBeUsing errors))
            "Success"))))

(defn historical-data-params-formatter
  "格式化历史数据参数"
  [signal-config units]
  (let [signal-vars (:signals signal-config)]
    {:xAxisCode  (when-let [x-axis-id (:signal_config_x signal-config)]
                   (some #(when (= (:signal_variable_id %) x-axis-id )
                            (:code %))
                         signal-vars))
     :yAxisCode  (when-let [y-axis-id (:signal_config_y signal-config)]
                   (some #(when (= (:signal_variable_id %) y-axis-id )
                            (:code %))
                         signal-vars))
     :bufferCode (:buffer_code signal-config)
     :signalVars (mapv
                  (fn [signal-var]
                    {:name (:variable_name signal-var)
                     :code (:code signal-var)
                     :unit (try-get-unit&factor units (:unit_id signal-var))})
                  signal-vars)}))

(defn gen-file-name
  "生成导出的文件名
  - Note:
    因为在操作系统中生成的文件名有字符的上限(似乎是260), 过长时裁切部分内容"
  [codes file-type]
  (if (> (count codes) 5)
    ;; 最多的情况只生成50位的字符串
    (str
     "sample_"
     (subs
      (cstr/join ""
                 ;; 删除试样code中的code头
                 (mapv #(cstr/replace % "sample" "") codes))
      0 50)
     "."
     file-type)
    (str (cstr/join "" codes)
         "."
         file-type)))

(defn gen-file-name-by-names
  "根据试样名称生成导出的文件名
  - Note:
    因为在操作系统中生成的文件名有字符的上限(似乎是260), 过长时裁切部分内容"
  [names file-type]
  (if (> (count names) 5)
    ;; 最多的情况只生成50位的字符串
    (str
     (subs
      (cstr/join ""
                 names)
      0 50)
     "."
     file-type)
    (str (cstr/join "" names)
         "."
         file-type)))

(defn export-excel-data
  "导出报表"
  [conn param]
  (let [config (get-report-config conn param)
        units  (all-units conn param)]
    (taskserver-export
     {:templateName                 (gen-class-name param)
      :exportReportFilePath         (str (:export_path config)
                                         file-separator
                                         (:export_name config)
                                         (gen-file-name (:codes param) (:file_type config)))
      :sampleInsts                  (get-report-sample-insts conn
                                                             (:codes param)
                                                             (:input_config config)
                                                             units)
      :resultVars                   (get-report-result-vars conn
                                                            (:result_config config)
                                                            units)
      ;; FIXME: 当前statistic_config保存的为字符串
      :resultVarStatisticalPatterns (mapv parse-long (:statistic_config config))
      :historicalDataParams         (historical-data-params-formatter
                                     (:signal_config config)
                                     units)})))

(defn get-excel-export-params
  "获取Excel导出参数，不直接调用C#接口"
  [conn param]
  (let [config (get-report-config conn param)
        units  (all-units conn param)
        sample-insts (get-report-sample-insts conn
                                              (:codes param)
                                              (:input_config config)
                                              units)
        result-vars (get-report-result-vars conn
                                            (:result_config config)
                                            units)
        result-var-statistical-patterns (mapv parse-long (:statistic_config config))
        historical-data-params (historical-data-params-formatter
                                (:signal_config config)
                                units)
        template-name (gen-class-name param)
        sample-names (mapv :name sample-insts)
        export-file-path (str (:export_path config)
                              file-separator
                              (:export_name config)
                              (gen-file-name-by-names sample-names (:file_type config)))]
    {:template-name template-name
     :export-file-path export-file-path
     :sample-insts sample-insts
     :result-vars result-vars
     :result-var-statistical-patterns result-var-statistical-patterns
     :historical-data-params historical-data-params
     :total-sample-count (count sample-insts)}))


(defn- taskserver-export-csv-async
  "调用taskServer异步导出csv接口"
  [form-body]
  (-> (client/post "http://localhost:5002/export/csv/async"
                   {:form-params  form-body
                    :content-type :json
                    :accept       :json
                    :debug        true})
      (:body)
      (utils/read-json)))

(defn- taskserver-get-csv-status
  "查询taskServer CSV导出状态"
  [task-id]
  (-> (client/get (str "http://localhost:5002/export/csv/status/" task-id)
                  {:accept :json
                   :debug  true})
      (:body)
      (utils/read-json)))

(defn export-csv-async
  "异步导出csv"
  [conn {:keys [sample_code] :as param}]
  (let [csv-config (db-utils/find-detail-complex
                    conn
                    {:table-name export-confg-table-name
                     :conds      {:delete_flag  0
                                  :default_flag 1
                                  :export_type  "csv"}})
        _          (when-not (seq csv-config) (throw-error 4000))
        _          (when-not (io-utils/path-is-file? (:export_path csv-config)) (throw-error 4001))
        result     (taskserver-export-csv-async
                    {:templateName      (gen-class-name param)
                     :exportCsvFilePath (str (:export_path csv-config) file-separator)
                     :exportCsvFileName (:export_name csv-config)
                     :sampleInstCodes   [sample_code]
                     :bufferCode        (:csv_buffer csv-config)})]
    result))

(defn get-csv-export-status
  "查询CSV导出状态"
  [conn {:keys [task_id]}]
  (when-not task_id
    (throw-error 4002 "任务ID不能为空"))
  (taskserver-get-csv-status task_id))

(defn export-csv
  "导出csv（同步版本，保持兼容性）"
  [conn {:keys [sample_code] :as param}]
  (let [csv-config (db-utils/find-detail-complex
                    conn
                    {:table-name export-confg-table-name
                     :conds      {:delete_flag  0
                                  :default_flag 1
                                  :export_type  "csv"}})
        _          (when-not (seq csv-config) (throw-error 4000))
        _          (when-not (io-utils/path-is-file? (:export_path csv-config))  (throw-error 4001))]
    (taskserver-export-csv
     {:templateName      (gen-class-name param)
      :exportCsvFilePath (str (:export_path csv-config) file-separator)
      :exportCsvFileName (:export_name csv-config)
      :sampleInstCodes   [sample_code]
      :bufferCode        (:csv_buffer csv-config)})))

(defn export-excel-all-data
  "临时接口"
  [conn param]
  (throw-error 5000)
  #_(let [sample-data (get-sample-data conn param)
          units (dimension-units-list conn param)
          variable-signal  (db-utils/find-list-complex
                            conn
                            {:table-name variable-signal-table-name
                             :select-cols ["variable_name","code","dimension_id","unit_id"]
                             :conds {:delete_flag 0}})]

      (export-signal-data
       (map (fn [i]
              (let [daq_data (:daq_data i)]
                {:name (:name i)
                 :daq_data
                 (map
                  (fn [daq]
                    (let [var-values (-> daq  :message :UIParams :VarValues)
                          createtime (-> daq :createtime)]
                      (remove nil?
                              (concat
                               [{:variable_name "创建时间"
                                 :Value (or createtime "11")}]
                               (map (fn [var]
                                      (some #(when (= (:code %) (:Code var))
                                               (merge %
                                                      {:Value (:Value var)})) variable-signal))
                                    var-values)))))
                  daq_data)}))
            sample-data)
       (str io-utils/current-desktop-directory  "\\信号变量数据.xlsx"))))

(defn export-confg-list
  "报表配置列表"
  [conn param]
  (mapv #(utils/parse-special-key-in-map (mapv keyword tab-columns) %)
        (db-utils/find-list
         conn
         {:table-name export-confg-table-name})))

(defn export-confg-save
  "报表配置保存"
  [conn param]
  (conman/with-transaction [*db*]
    (let [id (if (seq (:id param)) (:id param) (str (random-uuid)))
          data (->
                (utils/serialize-special-key-in-map (mapv keyword tab-columns) param)
                (assoc  :id id
                        :created_user_id (:user_id param)
                        :updated_user_id (when (seq (:id param)) (:user_id param)))
                (dissoc :project_id :template_id :user_id))]
      (save-log
       (merge param
              {:content_key :save_report
               :content (:export_name param)}))
      (db-utils/insert-or-update!
       conn
       export-confg-table-name
       data
       "id")
      (when (and (:default_flag param)
                 (pos? (:default_flag param)))
        (export-confg-redefault conn (assoc param :id id)))
      true)))


(defn export-confg-del
  "报表配置删除"
  [conn param]
  (let [data (db-utils/find-detail
              conn
              {:table-name export-confg-table-name
               :id-name "id"
               :id (:id param)})]
    (save-log
     (merge param
            {:content_key :del_report
             :content (:export_name data)}))
   (db-utils/update!
    conn
    {:table-name export-confg-table-name
     :updates    {:delete_flag 1
                  :updated_user_id (:id param)}
     :id-name    "id"
     :id         (:id param)})))

(defn export-confg-redefault
  "报表配置-修改默认"
  [conn param]
  (conman/with-transaction [*db*]
    (let [ids  (->>
                {:table-name export-confg-table-name
                 :select-cols ["id"]
                 :conds {:delete_flag 0
                         :export_type (:export_type param)}}
                (db-utils/find-list-complex conn)
                (map :id))]
      (db-utils/batch-update!
       conn
       {:table-name export-confg-table-name
        :updates {:default_flag 0
                  :updated_user_id (:id param)}
        :id-name "id"
        :ids ids})
      (db-utils/update!
       conn
       {:table-name export-confg-table-name
        :updates {:default_flag (:default_flag param)
                  :updated_user_id (:id param)}
        :id-name "id"
        :id (:id param)}))))

;; ---------------------------------------------------------------------
(defn import-excel-data
  "导入xsl数据 用于测试"
  [conn body]
  (let [excel-data  (excel-utils/get-sheet-info (:path body))
        signal-data (excel-utils/read-xls (:path body) 0)]
    (run!
     (fn [signal-val]
       (let [varvalues (map-indexed (fn [idx signal-name]
                                      {:Code  signal-name
                                       :Name  signal-name
                                       :Value (get signal-val idx)})
                                    (:signal-data excel-data))]
         (db-utils/insert!
          conn
          {:table-name "MessagesDAQ"
           :inserter {:sample_instance_code (:sample_code body)
                      :message (utils/write-json
                                {:ProcessID (str "project_" (:project_id body))
                                 :SubTaskID (:daq_id body)
                                 :UICmd     "signalVar"
                                 :UIParams  {:VarValues varvalues}})
                      :daqID (:daq_id body)}})))
     signal-data)))
