# CSV导出性能优化问题分析

## 问题描述

试样导出CSV时间过长，从16:18到24:18，整整8个小时，500个CSV文件（1分钟一个，100万行）。前端无法等待这么久，需要实现导出任务的方式，或者加快导出速度。

## 当前架构分析

### 1. 现有导出流程

#### 前端调用流程
```javascript
// 前端调用导出CSV接口
export const getExportCSV = (data) => {
    return templateAxios({
        url: API_CONFIG.report_export_csv.url,
        method: METHOD_TYPE.POST,
        data
    })
}
```

#### 后端处理流程
1. **Clojure后端接收请求** (`clj-backend/modules/result_report/service.clj`)
   - `export-csv` 函数处理请求
   - 获取CSV配置信息
   - 调用TaskServer的CSV导出接口

2. **TaskServer处理** (`TaskServer/ApiServer/Routes/Report/Service.cs`)
   - `CsvExport` 方法执行实际导出
   - 每个试样代码生成多个CSV文件（每个文件最多100万行）
   - 同步处理，直到所有文件生成完成

### 2. 性能瓶颈分析

#### 数据量问题
- 500个CSV文件，每个100万行
- 总计约5亿行数据
- 单线程顺序处理

#### 架构问题
- **同步处理**：前端发起请求后必须等待所有文件生成完成
- **单线程处理**：TaskServer中的CsvExport方法是单线程执行
- **内存占用**：大量数据同时处理可能导致内存压力
- **无进度反馈**：前端无法获知导出进度

#### 数据库查询问题
```csharp
// 当前实现：分页查询但仍然是同步处理
var pagedData = template.Db.GetHistoricalDataPaged(tableName, fields, exportedRows / maxRowsPerFile, maxRowsPerFile);
```

### 3. 现有任务管理系统

系统已有完善的任务管理机制：

#### 多任务管理器 (`clj_scheduler/context.clj`)
- `IMultiTaskMgr` 接口定义任务管理
- 支持任务状态管理：ready、running、finished、error
- 支持任务暂停、恢复、终止
- 异步消息处理机制

#### 子任务系统 (`SubTasks/`)
- 支持长时间运行的后台任务
- 任务状态实时更新到前端
- 支持任务进度查询

## 解决方案分析

### 方案1：基于现有任务系统的异步导出

**优势：**
- 复用现有任务管理架构
- 前端可以实时查询进度
- 支持任务暂停、恢复、取消
- 架构一致性好

**实现要点：**
- 创建专门的CSV导出子任务
- 利用现有的任务状态管理
- 前端轮询查询任务状态
- 支持批量文件生成进度反馈

### 方案2：独立的导出任务队列

**优势：**
- 专门针对导出优化
- 可以实现更细粒度的进度控制
- 支持并发导出多个文件
- 更灵活的任务管理

**实现要点：**
- 创建独立的任务队列服务
- 实现任务状态持久化
- 提供任务查询和管理接口
- 支持文件级别的并发处理

### 方案3：流式导出 + 分片下载

**优势：**
- 减少内存占用
- 支持断点续传
- 用户体验更好
- 可以边生成边下载

**实现要点：**
- 实现流式CSV生成
- 支持分片文件下载
- 前端支持多文件下载管理
- 实现下载进度显示

## 推荐方案

**推荐方案1：基于现有任务系统的异步导出**

理由：
1. **架构一致性**：充分利用现有的任务管理系统，保持架构统一
2. **开发成本低**：复用现有代码，减少开发工作量
3. **稳定性高**：现有任务系统已经过验证，稳定可靠
4. **功能完整**：支持任务的完整生命周期管理

## 技术风险评估

### 方案1风险
- **低风险**：基于现有系统，技术风险可控
- 需要注意任务状态同步的准确性
- 需要处理大量文件的存储管理

### 方案2风险
- **中等风险**：需要开发新的任务队列系统
- 可能与现有架构产生冲突
- 需要额外的维护成本

### 方案3风险
- **高风险**：需要大幅修改现有导出逻辑
- 前端需要支持复杂的下载管理
- 技术实现复杂度高

## 性能优化建议

无论采用哪种方案，都应该考虑以下优化：

1. **并发处理**：支持多个CSV文件并发生成
2. **数据库优化**：优化历史数据查询性能
3. **内存管理**：避免大量数据同时加载到内存
4. **文件压缩**：考虑对生成的CSV文件进行压缩
5. **缓存机制**：对重复查询的数据进行缓存

## 下一步行动

1. 确认采用的解决方案
2. 详细设计技术实现方案
3. 评估开发工作量和时间计划
4. 制定测试策略
