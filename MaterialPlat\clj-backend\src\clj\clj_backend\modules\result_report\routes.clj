(ns clj-backend.modules.result-report.routes
  "导出报表需要的接口"
  (:require
   [clj-backend.common.result :refer [ok]]
   [clj-backend.common.template-utils
    :refer [to-real-spec use-template-execute]]
   [clj-backend.modules.result-report.service :as service]
   [spec-tools.core :as st]
   [spec-tools.data-spec :as ds]))

(def redefault-spec
  {:export_type string?
   :id string?})

(def ^:private report-spec
  {(ds/opt :id)               string?
   :export_name               string?
   :export_path               string?
   :export_type               string?
   :file_type                 string?
   (ds/opt :default_flag)     number?
   (ds/opt :input_config)     [any?]
   (ds/opt :result_config)    [any?]
   (ds/opt :signal_config)    map?
   (ds/opt :statistic_config) [any?]
   (ds/opt :pdf_config)       [any?]
   (ds/opt :remark)           string?
   ;; csv config
   (ds/opt :csv_buffer)       string?
   (ds/opt :csv_database)     (st/spec #{"realTime" "peakValley" "abnormal"})})

(def ^:private get-variable-input-spec
  {:codes                [string?]
   :export_type          string?
   (ds/opt :project_id)  :base/id})

(def ^:private export-csv-spec
  {:sample_code string?
   :project_id  :base/id})

(def ^:private export-csv-status-spec
  {:task_id string?})

(def ^:private get-variable-result-or-sample-info
{(ds/opt :codes)      [string?]
 (ds/opt :project_id) :base/id})

(def ^:private import-excel-data-spec
{:project_id  int?
 :sample_code string?
 :daq_id      string?
 :path        string?})

(defn routes []
["/report"
 {:swagger {:tags ["导出报表相关接口"]}}

 ["/export/config"
  {:get {:summary    "报表配置查看"
         :parameters {:query (to-real-spec)}
         :handler    (fn [{{:keys [query]} :parameters}]
                       (ok (use-template-execute
                            query
                            service/export-confg-list query)))}

   :post {:summary    "报表编辑或新增"
          :parameters {:body (to-real-spec report-spec)}
          :handler    (fn [{{:keys [body]} :parameters}]
                        (ok (use-template-execute
                             body
                             service/export-confg-save body)))}

   :delete {:summary    "删除报表"
            :parameters {:body (to-real-spec redefault-spec)}
            :handler    (fn [{{:keys [body]} :parameters}]
                          (ok (use-template-execute
                               body
                               service/export-confg-del body)))}}]

 ["/export/redefault"
  {:post {:summary    "修改delault"
          :parameters {:body (to-real-spec
                              (merge redefault-spec
                                     {:default_flag number?}))}
          :handler    (fn [{{:keys [body]} :parameters}]
                        (ok (use-template-execute
                             body
                             service/export-confg-redefault body)))}}]

 ["/export/excel/data"
  {:post {:summary    "导出报表"
          :parameters {:body get-variable-input-spec}
          :handler    (fn [{{:keys [body]} :parameters}]
                        (ok (use-template-execute
                             body
                             service/export-excel-data body)))}}]

 ["/export/excel/params"
  {:post {:summary    "获取Excel导出参数"
          :parameters {:body get-variable-input-spec}
          :handler    (fn [{{:keys [body]} :parameters}]
                        (ok (use-template-execute
                             body
                             service/get-excel-export-params body)))}}]

 ["/export/csv/data"
  {:post {:summary    "导出csv"
          :parameters {:body export-csv-spec}
          :handler    (fn [{{:keys [body]} :parameters}]
                        (ok (use-template-execute
                             body
                             service/export-csv body)))}}]

 ["/export/csv/async"
  {:post {:summary    "异步导出csv"
          :parameters {:body export-csv-spec}
          :handler    (fn [{{:keys [body]} :parameters}]
                        (ok (use-template-execute
                             body
                             service/export-csv-async body)))}}]

 ["/export/csv/status"
  {:post {:summary    "查询CSV导出状态"
          :parameters {:body export-csv-status-spec}
          :handler    (fn [{{:keys [body]} :parameters}]
                        (ok (use-template-execute
                             body
                             service/get-csv-export-status body)))}}]

 ["/export/excel/all/data"
  {:post {:summary    "导出某个试样的信号变量数据"
          :parameters {:body {:codes [string?]
                              :project_id :base/id}}
          :handler    (fn [{{:keys [body]} :parameters}]
                        (ok (use-template-execute
                             body
                             service/export-excel-all-data body)))}}]

 ["/variable"
  ["/input"
   {:post {:summary    "获取输入变量数据"
           :parameters {:body get-variable-input-spec}
           :handler    (fn [{{:keys [body]} :parameters}]
                         (ok (use-template-execute
                              body
                              service/get-variable-input body)))}}]
  ["/result"
   {:post {:summary    "获取结果变量数据"
           :parameters {:body get-variable-result-or-sample-info}
           :handler    (fn [{{:keys [body]} :parameters}]
                         (ok (use-template-execute
                              body
                              service/get-variable-result body)))}}]]
 ["/sample-info"
  {:post {:summary    "获取试样参数信息"
          :parameters {:body get-variable-result-or-sample-info}
          :handler    (fn [{{:keys [body]} :parameters}]
                        (ok (use-template-execute
                             body
                             service/get-sample-info body)))}}]
 ["/import/excel"
  {:post {:summary "测试数据，导入报表数据"
          :parameters {:body import-excel-data-spec}
          :handler (fn [{{:keys [body]} :parameters}]
                     (ok (use-template-execute
                          body
                          service/import-excel-data body)))}}]])
