# 方案1细化实施方案：基于现有任务系统的异步导出

## 变更文件清单

### 新增文件
1. **SubTaskList/tasks/SubTaskCsvExport.cs** - CSV导出子任务类
2. **clj-backend/src/clj/clj_backend/modules/result_report/csv_export_service.clj** - CSV导出异步服务
3. **react-electron/src/components/CsvExportProgressModal.js** - 进度显示组件
4. **react-electron/src/components/CsvExportTaskManager.js** - 任务管理组件

### 修改文件
1. **clj-backend/src/clj/clj_backend/modules/result_report/service.clj** - 添加异步导出函数
2. **clj-backend/src/clj/clj_backend/modules/result_report/routes.clj** - 添加新的API路由
3. **react-electron/src/utils/services.js** - 添加异步导出相关API调用
4. **react-electron/src/utils/serviceConstants.js** - 添加新的API配置
5. **react-electron/src/pages/resultReport/index.js** - 修改导出按钮逻辑
6. **SubTasks/MQ.cs** - 注册新的子任务类型

### 数据库变更
1. **新增表：csv_export_tasks** - 任务状态管理表

## 分阶段实施计划

### 阶段一：创建CSV导出子任务类 (3-4天)

#### 1.1 创建SubTaskCsvExport.cs
**文件路径**: `SubTaskList/tasks/SubTaskCsvExport.cs`

**核心功能**:
- 实现ISubTask接口
- 支持进度更新和状态管理
- 集成现有CSV导出逻辑
- 异常处理和重试机制

**关键代码结构**:
```csharp
public class SubTaskCsvExport : ISubTask
{
    // 基础属性
    private string? _processID;
    private string? _subtaskID;
    private string? _className;
    private TaskStatus _taskState = TaskStatus.READY;
    
    // 导出参数
    private ExportCsvParams _exportParams;
    
    // 进度信息
    private int _totalFiles = 0;
    private int _completedFiles = 0;
    private long _totalRows = 0;
    private long _processedRows = 0;
    
    // 消息总线配置
    public bool Sub_TASK_MGR_CMD { get; set; } = true;
    public bool Sub_TASK_MGR_CMD_Other { get; set; } = false;
    public bool Sub_TASK_HARDWARE_DATA { get; set; } = false;
    public bool Sub_TOPIC_FROM_UI { get; set; } = false;
    
    // 核心方法实现
    public bool Run(SubTaskCmdParams Params);
    public bool Finish(SubTaskCmdParams Params);
    public bool Abort(SubTaskCmdParams Params);
    public bool Pause(SubTaskCmdParams Params);
    public bool Resume(SubTaskCmdParams Params);
}
```

#### 1.2 实现核心导出逻辑
**基于现有CsvExport方法改造**:
```csharp
private async Task ExecuteCsvExport()
{
    var template = ITemplate.GetTemplateByName(_exportParams.TemplateName);
    if (template?.IsProject != true) return;
    
    const int maxRowsPerFile = 1_000_000;
    
    foreach (var sampleInstCode in _exportParams.SampleInstCodes)
    {
        var dynamicDataTable = template.Db.GetTable(sampleInstCode, _exportParams.BufferCode);
        var tableName = sampleInstCode + _exportParams.BufferCode;
        var totalRows = template.Db.GetHistoricalDataCount(tableName);
        var fields = dynamicDataTable.Fields;
        
        _totalRows += totalRows;
        _totalFiles += (int)Math.Ceiling((double)totalRows / maxRowsPerFile);
        
        // 更新总体进度信息
        UpdateProgress();
        
        int fileIndex = 1;
        int exportedRows = 0;
        
        while (exportedRows < totalRows)
        {
            // 检查任务状态（暂停/取消）
            if (_taskState == TaskStatus.PAUSED)
            {
                await WaitForResume();
            }
            
            if (_taskState == TaskStatus.CANCELLED)
            {
                return;
            }
            
            int rowsThisFile = Math.Min(maxRowsPerFile, totalRows - exportedRows);
            string fileName = $"{_exportParams.ExportCsvFilePath}{_exportParams.ExportCsvFileName}{sampleInstCode}_{fileIndex}.csv";
            
            // 生成单个CSV文件
            await GenerateCsvFile(fileName, template, tableName, fields, exportedRows, rowsThisFile);
            
            exportedRows += rowsThisFile;
            _processedRows += rowsThisFile;
            fileIndex++;
            _completedFiles++;
            
            // 更新进度
            UpdateProgress();
        }
    }
}

private void UpdateProgress()
{
    var overallProgress = _totalRows > 0 ? (int)((_processedRows * 100) / _totalRows) : 0;
    
    // 发送进度更新到前端
    var progressInfo = new
    {
        progress = overallProgress,
        completed_files = _completedFiles,
        total_files = _totalFiles,
        processed_rows = _processedRows,
        total_rows = _totalRows,
        status = _taskState.ToString().ToLower()
    };
    
    var progressJson = JsonSerializer.Serialize(progressInfo);
    var uiCmd = new UICmdParams(_processID!, _subtaskID!, "csvExportProgress", 
        JsonDocument.Parse(progressJson).RootElement);
    
    ISystemBus.SendToUICmdTopic(JsonSerializer.Serialize(uiCmd));
}
```

#### 1.3 注册子任务类型
**修改文件**: `SubTasks/MQ.cs`

在`getTask`方法中添加新的子任务类型：
```csharp
case "SubTaskCsvExport":
    return serviceProvider.GetService<SubTaskCsvExport>()!;
```

### 阶段二：后端接口开发 (2-3天)

#### 2.1 创建异步导出服务
**文件路径**: `clj-backend/src/clj/clj_backend/modules/result_report/csv_export_service.clj`

```clojure
(ns clj-backend.modules.result-report.csv-export-service
  (:require [clj-backend.utils.db-utils :as db-utils]
            [clj-backend.utils.utils :as utils]
            [clj-backend.modules.template.template-utils :as tu]
            [clj-scheduler.executer :as executer]))

(def csv-export-tasks-table "csv_export_tasks")

(defn create-csv-export-task
  "创建CSV导出任务"
  [conn {:keys [sample_code] :as param}]
  (let [task-id (str "csv-export-" (System/currentTimeMillis))
        csv-config (get-csv-config conn param)
        task-data {:task_id task-id
                   :template_name (tu/gen-class-name param)
                   :sample_code sample_code
                   :status "ready"
                   :progress 0
                   :total_files 0
                   :completed_files 0
                   :total_rows 0
                   :processed_rows 0
                   :created_time (java.time.Instant/now)
                   :updated_time (java.time.Instant/now)}]
    
    ;; 保存任务信息到数据库
    (db-utils/insert! conn {:table-name csv-export-tasks-table
                           :data task-data})
    
    ;; 创建任务上下文
    (let [context (create-csv-export-context param csv-config task-id)]
      ;; 启动异步任务
      (executer/execute-context conn context (tu/gen-class-name param) task-id))
    
    ;; 返回任务信息
    {:task_id task-id
     :status "started"
     :message "CSV导出任务已启动"}))

(defn create-csv-export-context
  "创建CSV导出任务上下文"
  [param csv-config task-id]
  [{:task-type :csv-export
    :id task-id
    :name "CSV导出任务"
    :status :ready
    :params {:export-params {:templateName (tu/gen-class-name param)
                            :exportCsvFilePath (str (:export_path csv-config) 
                                                   java.io.File/separator)
                            :exportCsvFileName (:export_name csv-config)
                            :sampleInstCodes [(:sample_code param)]
                            :bufferCode (:csv_buffer csv-config)}
             :task-id task-id}}])

(defn get-csv-export-progress
  "查询CSV导出进度"
  [conn {:keys [task_id]}]
  (if-let [task (db-utils/find-detail conn {:table-name csv-export-tasks-table
                                           :conds {:task_id task_id}})]
    task
    {:error "任务不存在"}))

(defn get-csv-export-tasks
  "获取CSV导出任务列表"
  [conn param]
  (db-utils/find-list conn {:table-name csv-export-tasks-table
                           :order-by [[:created_time :desc]]
                           :limit 50}))

(defn update-task-progress
  "更新任务进度"
  [conn task-id progress-data]
  (db-utils/update! conn {:table-name csv-export-tasks-table
                         :updates (merge progress-data 
                                        {:updated_time (java.time.Instant/now)})
                         :conds {:task_id task-id}}))
```

#### 2.2 修改现有service.clj
**文件路径**: `clj-backend/src/clj/clj_backend/modules/result_report/service.clj`

添加异步导出函数：
```clojure
(defn export-csv-async
  "异步导出CSV"
  [conn param]
  (csv-export-service/create-csv-export-task conn param))
```

#### 2.3 添加新的API路由
**文件路径**: `clj-backend/src/clj/clj_backend/modules/result_report/routes.clj`

```clojure
;; 异步导出CSV
["/export/csv/async"
 {:post {:summary "异步导出CSV"
         :parameters {:body export-csv-spec}
         :handler (fn [{{:keys [body]} :parameters}]
                    (ok (use-template-execute
                         body
                         service/export-csv-async body)))}}]

;; 查询导出进度
["/export/csv/progress"
 {:get {:summary "查询CSV导出进度"
        :parameters {:query {:task_id string?}}
        :handler (fn [{{:keys [query]} :parameters}]
                   (ok (use-template-execute
                        query
                        csv-export-service/get-csv-export-progress query)))}}]

;; 获取任务列表
["/export/csv/tasks"
 {:get {:summary "获取CSV导出任务列表"
        :handler (fn [_]
                   (ok (use-template-execute
                        {}
                        csv-export-service/get-csv-export-tasks {})))}}]
```

### 阶段三：前端界面开发 (3-4天)

#### 3.1 添加API服务函数
**文件路径**: `react-electron/src/utils/services.js`

```javascript
// 异步导出CSV
export const startCsvExportTask = async (data) => {
    return templateAxios({
        url: API_CONFIG.csv_export_async.url,
        method: METHOD_TYPE.POST,
        data
    })
}

// 查询导出进度
export const getCsvExportProgress = async (params) => {
    return templateAxios({
        url: API_CONFIG.csv_export_progress.url,
        method: METHOD_TYPE.GET,
        params
    })
}

// 获取任务列表
export const getCsvExportTasks = async () => {
    return templateAxios({
        url: API_CONFIG.csv_export_tasks.url,
        method: METHOD_TYPE.GET
    })
}
```

#### 3.2 添加API配置
**文件路径**: `react-electron/src/utils/serviceConstants.js`

```javascript
csv_export_async: {
    url: '/report/export/csv/async',
    post: '异步导出CSV'
},
csv_export_progress: {
    url: '/report/export/csv/progress',
    get: '查询CSV导出进度'
},
csv_export_tasks: {
    url: '/report/export/csv/tasks',
    get: '获取CSV导出任务列表'
}
```

#### 3.3 创建进度显示组件
**文件路径**: `react-electron/src/components/CsvExportProgressModal.js`

```javascript
import React, { useState, useEffect } from 'react';
import { Modal, Progress, Button, message } from 'antd';
import { getCsvExportProgress } from '../utils/services';

const CsvExportProgressModal = ({ taskId, visible, onClose }) => {
    const [progress, setProgress] = useState(0);
    const [status, setStatus] = useState('running');
    const [filesCompleted, setFilesCompleted] = useState(0);
    const [totalFiles, setTotalFiles] = useState(0);
    const [processedRows, setProcessedRows] = useState(0);
    const [totalRows, setTotalRows] = useState(0);

    useEffect(() => {
        let interval;
        
        if (visible && taskId) {
            // 立即查询一次
            queryProgress();
            
            // 设置定时查询
            interval = setInterval(queryProgress, 2000);
        }
        
        return () => {
            if (interval) {
                clearInterval(interval);
            }
        };
    }, [visible, taskId]);

    const queryProgress = async () => {
        try {
            const result = await getCsvExportProgress({ task_id: taskId });
            
            if (result.error) {
                message.error(result.error);
                return;
            }
            
            setProgress(result.progress || 0);
            setStatus(result.status || 'running');
            setFilesCompleted(result.completed_files || 0);
            setTotalFiles(result.total_files || 0);
            setProcessedRows(result.processed_rows || 0);
            setTotalRows(result.total_rows || 0);
            
            // 任务完成或失败时停止查询
            if (result.status === 'completed') {
                message.success('CSV导出完成！');
                setTimeout(onClose, 2000);
            } else if (result.status === 'failed') {
                message.error('CSV导出失败：' + (result.error_message || '未知错误'));
            }
        } catch (error) {
            console.error('查询进度失败:', error);
        }
    };

    const getStatusText = (status) => {
        const statusMap = {
            'ready': '准备中',
            'running': '导出中',
            'paused': '已暂停',
            'completed': '已完成',
            'failed': '失败',
            'cancelled': '已取消'
        };
        return statusMap[status] || status;
    };

    return (
        <Modal
            title="CSV导出进度"
            visible={visible}
            onCancel={onClose}
            footer={[
                <Button key="close" onClick={onClose}>
                    关闭
                </Button>
            ]}
            width={500}
        >
            <div style={{ padding: '20px 0' }}>
                <div style={{ marginBottom: '20px' }}>
                    <div>任务状态: <strong>{getStatusText(status)}</strong></div>
                    <div>任务ID: {taskId}</div>
                </div>
                
                <div style={{ marginBottom: '20px' }}>
                    <div>总体进度:</div>
                    <Progress 
                        percent={progress} 
                        status={status === 'failed' ? 'exception' : 'active'}
                    />
                </div>
                
                <div style={{ marginBottom: '10px' }}>
                    <div>文件进度: {filesCompleted} / {totalFiles}</div>
                    <div>数据行数: {processedRows.toLocaleString()} / {totalRows.toLocaleString()}</div>
                </div>
            </div>
        </Modal>
    );
};

export default CsvExportProgressModal;
```

#### 3.4 修改导出按钮逻辑
**文件路径**: `react-electron/src/pages/resultReport/index.js`

```javascript
import CsvExportProgressModal from '../../components/CsvExportProgressModal';

// 在组件中添加状态
const [csvExportTaskId, setCsvExportTaskId] = useState(null);
const [showProgressModal, setShowProgressModal] = useState(false);

// 修改导出CSV函数
const handleExportCSV = async () => {
    try {
        const result = await startCsvExportTask(exportParams);
        
        if (result.task_id) {
            setCsvExportTaskId(result.task_id);
            setShowProgressModal(true);
            message.success('CSV导出任务已启动，请查看进度');
        } else {
            message.error('启动导出任务失败');
        }
    } catch (error) {
        message.error('启动导出任务失败: ' + error.message);
    }
};

// 在render中添加进度模态框
<CsvExportProgressModal
    taskId={csvExportTaskId}
    visible={showProgressModal}
    onClose={() => {
        setShowProgressModal(false);
        setCsvExportTaskId(null);
    }}
/>
```

### 阶段四：数据库变更 (1天)

#### 4.1 创建任务状态表
**文件路径**: `clj-backend/resources/migrations_template/add-csv-export-tasks-table.up.sql`

```sql
CREATE TABLE csv_export_tasks (
    task_id VARCHAR(50) PRIMARY KEY,
    template_name VARCHAR(100),
    sample_code VARCHAR(100),
    status VARCHAR(20) DEFAULT 'ready',
    progress INTEGER DEFAULT 0,
    total_files INTEGER DEFAULT 0,
    completed_files INTEGER DEFAULT 0,
    total_rows BIGINT DEFAULT 0,
    processed_rows BIGINT DEFAULT 0,
    error_message TEXT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_time TIMESTAMP
);

CREATE INDEX idx_csv_export_tasks_status ON csv_export_tasks(status);
CREATE INDEX idx_csv_export_tasks_created_time ON csv_export_tasks(created_time);
```

## 测试计划

### 单元测试
1. SubTaskCsvExport类的各个方法测试
2. 异步导出服务函数测试
3. 前端组件功能测试

### 集成测试
1. 完整的异步导出流程测试
2. 进度查询和状态更新测试
3. 异常情况处理测试

### 性能测试
1. 大数据量导出测试（500个文件，100万行）
2. 并发任务测试
3. 内存和CPU使用率监控

## 部署注意事项

1. **数据库迁移**: 确保新的任务状态表正确创建
2. **子任务注册**: 确保新的子任务类型正确注册到系统中
3. **API路由**: 确保新的API路由正确配置
4. **前端构建**: 确保新的组件正确打包到生产版本中

## 关键技术细节

### 消息流程设计
```
前端请求 -> Clojure后端 -> 任务管理器 -> SubTaskCsvExport
    ↓           ↓              ↓              ↓
返回任务ID   创建任务记录    启动子任务      执行导出
    ↓           ↓              ↓              ↓
进度查询 <- 数据库查询   <- 状态更新    <- 进度消息
```

### 状态流转图
```
ready -> running -> completed
  ↓         ↓           ↑
  ↓      paused -------↑
  ↓         ↓
  ↓      cancelled
  ↓         ↓
  ↓      failed
  ↓         ↓
  └--> aborted
```

### 配置参数
```clojure
;; 在config.edn中添加
{:csv-export
 {:max-concurrent-tasks 3
  :progress-update-interval 5000
  :task-timeout 28800000
  :retry-attempts 3
  :cleanup-completed-after-days 7}}
```

## 质量保证

### 代码审查检查点
1. **异常处理**: 确保所有可能的异常都被正确捕获和处理
2. **资源管理**: 确保文件流和数据库连接正确关闭
3. **线程安全**: 确保多线程环境下的数据一致性
4. **内存管理**: 避免内存泄漏和大对象长时间占用

### 性能监控指标
1. **任务执行时间**: 监控导出任务的执行时间
2. **内存使用**: 监控导出过程中的内存占用
3. **数据库性能**: 监控数据库查询的执行时间
4. **文件I/O性能**: 监控文件写入的性能

## 回滚方案

如果新功能出现问题，可以通过以下方式回滚：
1. **前端回滚**: 恢复原有的同步导出按钮逻辑
2. **后端回滚**: 禁用新的异步导出路由，保留原有同步接口
3. **数据库回滚**: 保留任务状态表，不影响现有功能
4. **子任务回滚**: 从任务系统中移除新的子任务类型

## 上线检查清单

### 部署前检查
- [ ] 代码审查完成
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 性能测试通过
- [ ] 数据库迁移脚本准备就绪

### 部署时检查
- [ ] 数据库迁移执行成功
- [ ] 新的子任务类型注册成功
- [ ] API路由配置正确
- [ ] 前端资源更新成功

### 部署后检查
- [ ] 异步导出功能正常
- [ ] 进度查询功能正常
- [ ] 任务状态更新正常
- [ ] 原有功能未受影响
- [ ] 系统性能指标正常
