# CSV导出异步任务优化 - 总体方案

## 问题分析

### 核心问题
- **导出时间过长**: 500个CSV文件（每个100万行），耗时8小时（16:18-24:18）
- **前端等待超时**: 前端无法等待如此长时间的同步操作
- **用户体验差**: 用户无法获知导出进度，无法进行其他操作

### 技术根因
1. **同步阻塞**: 当前`ExportCsv`接口是同步执行，前端必须等待完成
2. **无进度反馈**: 导出过程中没有进度信息返回给前端
3. **资源占用**: 长时间占用HTTP连接和服务器资源
4. **无任务管理**: 缺乏任务状态跟踪、暂停、取消等功能

### 现有架构分析
- **任务系统**: 已有完善的`ISubTask`接口和任务调度系统
- **消息总线**: 具备UI通信机制（`ISystemBus.SendToUICmdTopic`）
- **进度通知**: 支持实时状态更新到前端
- **异步执行**: Clojure后端支持异步任务执行（`execute-context`）

## 解决方案

### 方案1: 基于现有任务系统的异步导出（推荐）
**优势**: 
- 充分利用现有任务调度架构
- 支持暂停、恢复、取消操作
- 实时进度反馈
- 代码复用度高

**劣势**: 
- 需要修改多个组件
- 开发工期较长

**适用场景**: 长期维护，需要完整任务管理功能

### 方案2: 简单异步接口方案
**优势**: 
- 实现简单快速
- 改动最小
- 快速解决前端等待问题

**劣势**: 
- 功能有限
- 无法暂停/取消
- 进度反馈简单

**适用场景**: 快速解决当前问题，短期方案

### 方案3: 队列+后台服务方案
**优势**: 
- 高并发支持
- 任务持久化
- 系统解耦

**劣势**: 
- 架构复杂
- 需要额外组件
- 开发成本高

**适用场景**: 大规模并发导出需求

## 推荐方案详细设计

### 方案1: 基于现有任务系统的异步导出

#### 核心思路
1. 创建专用的`SubTaskCsvExport`子任务类
2. 修改导出接口为异步模式，立即返回任务ID
3. 通过消息总线实时推送进度信息
4. 提供任务状态查询接口

#### 技术实现路径
1. **C#端**: 实现`SubTaskCsvExport`类，继承`ISubTask`接口
2. **Clojure端**: 修改导出服务，支持异步任务创建
3. **API端**: 新增进度查询接口
4. **前端**: 轮询任务状态，显示进度条

#### 预期效果
- 导出请求立即返回（<1秒）
- 实时进度显示（文件数、行数、百分比）
- 支持任务管理（暂停、恢复、取消）
- 用户体验大幅提升

## 风险评估

### 技术风险
- **消息总线稳定性**: 长时间任务可能导致消息积压
- **内存占用**: 大量数据处理可能导致内存不足
- **并发冲突**: 多个导出任务同时执行的资源竞争

### 业务风险
- **数据一致性**: 异步导出过程中数据变更的处理
- **任务恢复**: 系统重启后任务状态的恢复
- **错误处理**: 导出失败时的通知和重试机制

## 实施计划

### 阶段一: 需求确认（1天）
- 确认具体功能需求
- 评估现有系统兼容性
- 制定详细技术方案

### 阶段二: 核心开发（5-7天）
- 实现`SubTaskCsvExport`类
- 修改Clojure导出服务
- 新增API接口
- 单元测试

### 阶段三: 集成测试（2-3天）
- 端到端测试
- 性能测试
- 错误场景测试

### 阶段四: 部署上线（1天）
- 生产环境部署
- 监控配置
- 用户培训

## 成功指标

### 性能指标
- 导出请求响应时间 < 1秒
- 进度更新频率 ≤ 5秒
- 系统资源占用合理

### 用户体验指标
- 用户可以在导出过程中进行其他操作
- 实时了解导出进度
- 支持任务管理操作

### 系统稳定性指标
- 长时间导出任务成功率 > 95%
- 系统重启后任务状态正确恢复
- 错误处理和通知及时准确
