# CSV异步导出前端实施指南

## 实施概述

基于后端已完成的异步导出功能，现在需要对前端进行适配，将原有的同步CSV导出改为异步模式，提供进度显示和更好的用户体验。

## 详细实施步骤

### 步骤1：API层适配

#### 1.1 修改API配置文件
**文件**: `react-electron/src/utils/serviceConstants.js`

在 `API_CONFIG` 对象中添加新的配置：

```javascript
// 在现有的 report_export_csv 配置后添加
report_export_csv_async: {
    url: '/report/export/csv/async',
    post: '异步导出csv'
},
report_export_csv_status: {
    url: '/report/export/csv/status',
    post: '查询CSV导出状态'
},
```

#### 1.2 添加API服务函数
**文件**: `react-electron/src/utils/services.js`

在 `getExportCSV` 函数后添加新函数：

```javascript
// 异步导出CSV
export const getExportCSVAsync = (data) => {
    return templateAxios({
        url: API_CONFIG.report_export_csv_async.url,
        method: METHOD_TYPE.POST,
        data
    })
}

// 查询CSV导出状态
export const getCsvExportStatus = (data) => {
    return templateAxios({
        url: API_CONFIG.report_export_csv_status.url,
        method: METHOD_TYPE.POST,
        data
    })
}
```

### 步骤2：UI层改造

#### 2.1 修改试样管理页面
**文件**: `react-electron/src/pages/layout/sample/index.js`

**导入新的API函数**：
在文件顶部的导入语句中添加：
```javascript
import {
    // ... 现有导入
    getExportCSVAsync, getCsvExportStatus
} from '@/utils/services'
```

**替换 onCSV 函数**：
找到现有的 `onCSV` 函数（约在第718行），完全替换为：

```javascript
const onCSV = async (menuData) => {
    if (menuData?.code) {
        let modal
        let pollTimer
        
        try {
            // 启动异步导出
            const exportResult = await getExportCSVAsync({
                sample_code: menuData?.code
            })
            
            const taskId = exportResult?.taskId || exportResult?.task_id
            if (!taskId) {
                message.error(t('启动导出任务失败'))
                return
            }
            
            // 显示进度模态框
            modal = Modal.info({
                title: t('导出CSV'),
                content: (
                    <div>
                        <p>{t('正在启动CSV导出任务...')}</p>
                        <Progress percent={0} status="active" />
                        <p style={{ fontSize: '12px', color: '#999', marginTop: '8px' }}>
                            任务ID: {taskId}
                        </p>
                    </div>
                ),
                okButtonProps: { style: { display: 'none' } },
                cancelButtonProps: { style: { display: 'none' } },
                maskClosable: false,
                width: 480
            })
            
            // 轮询查询进度
            const pollProgress = async () => {
                try {
                    const statusResult = await getCsvExportStatus({
                        task_id: taskId
                    })
                    
                    const { 
                        status, progress = 0, message: statusMessage = '', 
                        completedFiles = 0, totalFiles = 0, 
                        processedRows = 0, totalRows = 0,
                        errorMessage
                    } = statusResult
                    
                    // 构建进度显示文本
                    let progressText = t('正在导出CSV文件...')
                    if (totalFiles > 0) {
                        progressText += ` (${completedFiles}/${totalFiles} 文件)`
                    }
                    
                    let detailText = ''
                    if (totalRows > 0) {
                        detailText = `已处理 ${processedRows.toLocaleString()} / ${totalRows.toLocaleString()} 行数据`
                    } else if (statusMessage) {
                        detailText = statusMessage
                    }
                    
                    // 更新进度显示
                    modal?.update({
                        content: (
                            <div>
                                <p>{progressText}</p>
                                <Progress 
                                    percent={Math.min(progress, 100)} 
                                    status={status === 'Failed' ? 'exception' : 'active'} 
                                />
                                {detailText && (
                                    <p style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                                        {detailText}
                                    </p>
                                )}
                                <p style={{ fontSize: '12px', color: '#999', marginTop: '8px' }}>
                                    任务ID: {taskId}
                                </p>
                            </div>
                        )
                    })
                    
                    // 检查任务状态
                    if (status === 'Completed') {
                        // 导出完成
                        clearTimeout(pollTimer)
                        modal?.destroy()
                        message.success(t('导出CSV成功'))
                        return
                    } else if (status === 'Failed') {
                        // 导出失败
                        clearTimeout(pollTimer)
                        modal?.destroy()
                        message.error(t('导出CSV失败：') + (errorMessage || '未知错误'))
                        return
                    }
                    
                    // 继续轮询
                    pollTimer = setTimeout(pollProgress, 2000) // 每2秒查询一次
                } catch (error) {
                    console.error('查询导出状态失败:', error)
                    // 出错时延长间隔重试
                    pollTimer = setTimeout(pollProgress, 5000)
                }
            }
            
            // 开始轮询（1秒后开始第一次查询）
            pollTimer = setTimeout(pollProgress, 1000)
            
            // 添加模态框关闭时的清理逻辑
            const originalDestroy = modal.destroy
            modal.destroy = () => {
                clearTimeout(pollTimer)
                originalDestroy.call(modal)
            }
            
        } catch (err) {
            modal?.destroy()
            clearTimeout(pollTimer)
            console.error('CSV导出错误:', err)
            message.error(t('启动CSV导出失败：') + (err.message || '未知错误'))
        }
    }
}
```

### 步骤3：用户体验优化

#### 3.1 添加后台运行支持
可以在进度模态框中添加"后台运行"按钮，允许用户关闭进度框但任务继续执行：

```javascript
// 在模态框配置中添加footer
footer: [
    <Button key="background" onClick={() => {
        modal?.destroy()
        message.info(t('任务将在后台继续执行，任务ID：') + taskId)
    }}>
        {t('后台运行')}
    </Button>
]
```

#### 3.2 添加任务恢复功能
可以添加一个功能，允许用户通过任务ID恢复查看进度：

```javascript
// 可以在页面中添加一个"查看导出任务"的功能
const checkExportTask = async (taskId) => {
    try {
        const statusResult = await getCsvExportStatus({ task_id: taskId })
        // 显示任务状态...
    } catch (error) {
        message.error(t('查询任务失败：') + error.message)
    }
}
```

### 步骤4：测试验证

#### 4.1 功能测试
1. 测试异步导出启动是否正常
2. 测试进度显示是否准确
3. 测试任务完成和失败的处理
4. 测试网络异常时的重试机制

#### 4.2 用户体验测试
1. 验证进度显示的流畅性
2. 验证用户交互的友好性
3. 验证错误提示的准确性
4. 验证任务ID的显示和复制功能

#### 4.3 兼容性测试
1. 确保原有功能不受影响
2. 测试在不同浏览器中的表现
3. 测试在不同网络环境下的稳定性

## 实施注意事项

### 1. 错误处理
- 网络请求失败时要有重试机制
- API返回错误时要给出明确提示
- 轮询过程中的异常要妥善处理

### 2. 内存管理
- 及时清理定时器，避免内存泄漏
- 组件卸载时要清理相关资源
- 避免创建过多的轮询任务

### 3. 用户体验
- 进度显示要及时准确
- 错误信息要用户友好
- 支持用户中断和恢复操作

### 4. 性能考虑
- 轮询间隔要合理，避免过于频繁
- 进度更新要平滑，避免跳跃
- 大数据量时要考虑显示性能

## 验收标准

### 功能验收
- [ ] 异步导出启动成功率 100%
- [ ] 进度显示准确率 > 95%
- [ ] 任务完成通知及时性 < 5秒
- [ ] 错误处理覆盖率 100%

### 性能验收
- [ ] 导出启动响应时间 < 1秒
- [ ] 进度查询响应时间 < 500ms
- [ ] 前端内存使用稳定
- [ ] 轮询不影响其他功能

### 用户体验验收
- [ ] 用户操作流程顺畅
- [ ] 进度信息清晰易懂
- [ ] 错误提示准确友好
- [ ] 支持后台运行模式

## 部署说明

### 开发环境测试
1. 确保后端异步导出接口正常运行
2. 启动前端开发服务器
3. 测试完整的导出流程

### 生产环境部署
1. 构建前端代码
2. 部署到生产环境
3. 验证功能正常
4. 监控用户使用情况

## 后续优化建议

1. **任务管理界面**：可以考虑添加一个专门的任务管理页面
2. **批量导出**：支持多个试样的批量异步导出
3. **导出历史**：记录用户的导出历史和状态
4. **通知系统**：集成系统通知，任务完成时主动通知用户
