# CSV异步导出功能测试验证文档

## 测试环境准备

### 前置条件
1. TaskServer服务正常运行（端口5002）
2. Clojure后端服务正常运行
3. 数据库中有测试数据
4. 导出路径配置正确且可写

### 测试数据准备
- 测试项目ID: 需要有实际的项目数据
- 测试试样代码: 需要有实际的试样数据
- 确保有足够的历史数据用于导出测试

## API接口测试

### 1. 异步导出接口测试

#### TaskServer直接接口测试
```bash
# 测试异步导出接口
curl -X POST http://localhost:5002/export/csv/async \
  -H "Content-Type: application/json" \
  -d '{
    "templateName": "测试模板名称",
    "exportCsvFilePath": "C:/temp/",
    "exportCsvFileName": "test_export_",
    "sampleInstCodes": ["SAMPLE001"],
    "bufferCode": "BUFFER001"
  }'

# 期望响应
{
  "taskId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
  "status": "started",
  "message": "CSV导出任务已启动，请使用任务ID查询进度"
}
```

#### 状态查询接口测试
```bash
# 查询任务状态
curl -X GET http://localhost:5002/export/csv/status/{taskId}

# 期望响应
{
  "taskId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
  "status": "Running",
  "progress": 45,
  "message": "正在导出CSV文件",
  "createdTime": "2024-01-01T10:00:00",
  "updatedTime": "2024-01-01T10:05:00",
  "totalFiles": 10,
  "completedFiles": 4,
  "totalRows": 1000000,
  "processedRows": 450000,
  "errorMessage": null,
  "exportPath": null
}
```

#### 任务列表接口测试
```bash
# 获取所有任务
curl -X GET http://localhost:5002/export/csv/tasks

# 获取统计信息
curl -X GET http://localhost:5002/export/csv/statistics
```

### 2. Clojure后端接口测试

#### 异步导出接口
```bash
# 通过Clojure后端异步导出
curl -X POST http://localhost:3000/report/export/csv/async \
  -H "Content-Type: application/json" \
  -d '{
    "sample_code": "SAMPLE001",
    "project_id": 1
  }'
```

#### 状态查询接口
```bash
# 查询导出状态
curl -X POST http://localhost:3000/report/export/csv/status \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
  }'
```

## 功能测试用例

### 测试用例1: 正常异步导出流程
**测试步骤:**
1. 发起异步导出请求
2. 验证立即返回任务ID
3. 轮询查询任务状态
4. 验证进度信息更新
5. 验证导出完成状态
6. 检查导出文件是否生成

**期望结果:**
- 导出请求响应时间 < 1秒
- 任务状态正确变化: Started → Running → Completed
- 进度信息准确更新
- 导出文件正确生成

### 测试用例2: 大数据量导出测试
**测试步骤:**
1. 准备大量测试数据（>100万行）
2. 发起异步导出
3. 监控内存使用情况
4. 验证进度更新频率
5. 验证最终导出结果

**期望结果:**
- 内存使用稳定，无内存泄漏
- 进度更新及时准确
- 大文件正确分割
- 导出成功完成

### 测试用例3: 并发导出测试
**测试步骤:**
1. 同时发起多个导出任务
2. 验证任务状态独立管理
3. 检查资源竞争情况
4. 验证所有任务正确完成

**期望结果:**
- 支持至少3个并发任务
- 任务状态互不干扰
- 系统资源合理分配
- 所有任务成功完成

### 测试用例4: 错误场景测试
**测试步骤:**
1. 测试无效模板名称
2. 测试无效导出路径
3. 测试无效试样代码
4. 测试磁盘空间不足
5. 测试网络中断

**期望结果:**
- 错误信息准确返回
- 任务状态正确更新为Failed
- 错误日志详细记录
- 系统稳定不崩溃

### 测试用例5: 任务管理测试
**测试步骤:**
1. 创建多个任务
2. 验证任务列表功能
3. 验证统计信息准确性
4. 验证过期任务清理
5. 测试任务状态查询

**期望结果:**
- 任务列表正确显示
- 统计信息准确
- 过期任务自动清理
- 状态查询响应快速

## 性能测试

### 响应时间测试
- 异步导出接口响应时间 < 1秒
- 状态查询接口响应时间 < 500ms
- 任务列表接口响应时间 < 2秒

### 吞吐量测试
- 支持每分钟创建10个导出任务
- 支持每秒100次状态查询
- 支持3个并发导出任务

### 资源使用测试
- 内存使用增长 < 100MB
- CPU使用率 < 50%
- 磁盘IO合理分配

## 兼容性测试

### 向后兼容性
- 原有同步导出接口正常工作
- 现有客户端代码无需修改
- 数据格式保持一致

### 系统兼容性
- Windows环境正常运行
- .NET Core版本兼容
- Clojure版本兼容

## 测试结果记录

### 功能测试结果
- [ ] 正常异步导出流程: 通过/失败
- [ ] 大数据量导出测试: 通过/失败
- [ ] 并发导出测试: 通过/失败
- [ ] 错误场景测试: 通过/失败
- [ ] 任务管理测试: 通过/失败

### 性能测试结果
- [ ] 响应时间测试: 通过/失败
- [ ] 吞吐量测试: 通过/失败
- [ ] 资源使用测试: 通过/失败

### 兼容性测试结果
- [ ] 向后兼容性: 通过/失败
- [ ] 系统兼容性: 通过/失败

## 问题记录

### 发现的问题
1. 问题描述: 
   - 影响程度: 
   - 解决方案: 
   - 状态: 

2. 问题描述:
   - 影响程度:
   - 解决方案:
   - 状态:

### 优化建议
1. 建议内容:
   - 优先级:
   - 实施计划:

2. 建议内容:
   - 优先级:
   - 实施计划:

## 测试结论

### 整体评估
- 功能完整性: 
- 性能表现: 
- 稳定性: 
- 用户体验: 

### 上线建议
- [ ] 建议立即上线
- [ ] 建议修复问题后上线
- [ ] 建议进一步测试后上线
- [ ] 不建议上线

### 后续计划
1. 监控计划:
2. 维护计划:
3. 优化计划:
