using System.Text;
using System.Text.Json;
using Consts;
using Logging;
using MQ;
using SubTaskUtils;
using static SubTasks.ISubTask;
using System.Text.Json.Nodes;
using System.Reactive.Linq;
using System.Diagnostics;
using Scripting;
using static Scripting.ITemplate;
using ScriptEngine.InstantiatedTemplate.LogControl;
using ScriptEngine.InstantiatedTemplate.Hardware.MappingHardware;

namespace SubTasks.tasks.flowLogicTask;

/// <summary>
/// Class <c>SubTaskCreepControlCmd</c>
/// 蠕变下发命令子任务
/// </summary>
public class SubTaskCreepControlCmd : ISubTask
{
    //模板id
    private string? _processID;
    //子任务id
    private string? _subTaskID;
    //子任务需要的命令参数
    private SubTaskCmdParams? _subTaskCmdParams;
    //该子任务未完成
    private bool _unFinishFlag = true;
    //实例化类名
    private string? _className;
    //订阅的信号变量集合
    private readonly List<String> _selfTopics = new();
    //子任务日志前缀
    private const string CSC = "**蠕变下发命令子任务**";

    //监听回调
    public MQSubTaskSub[] subs { set; get; }
    //维护自身需要的订阅
    public bool Sub_TASK_MGR_CMD { get; set; } = true;
    public bool Sub_TASK_MGR_CMD_Other { get; set; } = true;
    public bool Sub_TASK_HARDWARE_CMD { get; set; } = true;
    public bool Sub_TASK_HARDWARE_DATA { get; set; } = true;
    public bool Sub_TOPIC_FROM_UI { get; set; } = true;
    public bool Sub_TOPIC_FROM_SCRIPT_CLIENT { get; set; } = true;
    public bool Sub_SelfTopic { get; set; } = true;
    public bool Sub_TOPIC_NOTIFY { get; set; } = false;
    ITemplate _templateInst;
    // 试验机-> 硬件连接器
    private string? _hwKey;
    private string? _hwSubId;
    private string? _version;

    //字典 蠕变基本命令
    private static readonly Dictionary<string, string>
        _commandMap = new Dictionary<string, string>
    {
        { "StartTest", "40;0;" },
        { "EndTest", "41;0;"},
        { "PauseTest", "42;0;"},
        { "ContinueTest", "43;0;"},
        { "ServoON", "45;0;"},
        { "ServoOff", "46;0;" },
        { "OnLine", "47;0;" },
        { "OffLine", "48;0;"},
        { "ClearBuffer", "49;0;"},
        { "DLLDataStart", "39;0;" },
        { "DLLDataStop", "38;0;" }
    };


    /// <summary>
    /// 子任务初始化
    /// </summary>
    /// <param name="paramatersString">子任务参数</param>
    /// <returns>子任务参数反序列化对象</returns>
    public SubTaskCmdParams? ImportParams(string paramatersString)
    {
        CCSSLogger.Logger.Info($"{CSC}ImportPrams:{paramatersString}");
        _subTaskCmdParams = System.Text.Json.JsonSerializer.Deserialize<SubTaskCmdParams>(paramatersString)!;
        return _subTaskCmdParams;
    }


    /// <summary>
    /// 主方法
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    public bool Run(SubTaskCmdParams param)
    {
        CCSSLogger.Logger.Info($"{CSC} 启动:{param}");

        //获取项目id 子任务id 实例化类名
        _processID = param.ProcessID;
        _subTaskID = param.SubTaskID;
        _className = param.ClassName;
        
        _templateInst = GetTemplateByName(_className!);
        // 通知UI当前子任务开始
        ISystemBus.SendToUIStatusTopic(UtilsForSubTasks.GenerateStatusUIJson(param.Cmd(), _processID!, _subTaskID!));

        //解析参数
        if (param?.SubTaskParams!.TryGetProperty("schedule", out JsonElement scheduleElement) ?? false)
        {
            if (scheduleElement.TryGetProperty("control_input_hwsubid", out var input_hwsubid) &&
                                scheduleElement.TryGetProperty("control_input_version", out var input_version)
                                )
            {
                input_hwsubid.TryGetProperty("value", out var hwSubId);
                _hwSubId = hwSubId.ToString();
                input_version.TryGetProperty("value", out var version);
                _version = version.ToString();
            }
            else
            {
                _hwSubId = "0";
                _version = "1.0";
            }

            if (scheduleElement.TryGetProperty("control_input_cmd", out var control_input_cmd))
            {
                if (control_input_cmd.TryGetProperty("value", out var cmdstr))
                {
                    CCSSLogger.Logger.Info($"{CSC} 启动:{cmdstr}");
                    MappingAxis mappingAxis = _templateInst!.MappingHardware.GetMappingAxis(
                        UtilsForSubTasks.ReadVarValue<object[]>(
                            _className!,
                            scheduleElement.GetProperty("control_input_axisIdx")
                        )
                    );
                    _hwKey = mappingAxis.Hwkey;
                    //axisIdx代表控制器index
                    int axisIdx = mappingAxis.RealIndex;
                    //CCSSLogger.Logger.Error($"{CSC} 轴id:{axisIdx}");
                    RecordLog("_hwKey:"+_hwKey);
                    FunctionCmd DataCommandCmd = new FunctionCmd(
                      FuncName: "CcssCreepCtrlStrCommand",
                      ProcessId: _processID!,
                      SubTaskID: _subTaskID!,
                      Params: new object[] { axisIdx, "" }
                    );
                    string mes = "";

                    switch (cmdstr.ToString())
                    {
                        case "StartTest":
                            DataCommandCmd.Params[1] = _commandMap["ClearBuffer"];
                            ISystemBus.SendToHWCmdTopic(JsonSerializer.Serialize(DataCommandCmd), _hwKey);
                            CCSSLogger.Logger.Info($"{CSC} 设备命令字符串:{JsonSerializer.Serialize(DataCommandCmd)}");
                            RecordLog(JsonSerializer.Serialize(DataCommandCmd));
                            DataCommandCmd.Params[1] = _commandMap["StartTest"];
                            ISystemBus.SendToHWCmdTopic(JsonSerializer.Serialize(DataCommandCmd), _hwKey);
                            CCSSLogger.Logger.Info($"{CSC} 设备命令字符串:{JsonSerializer.Serialize(DataCommandCmd)}");
                            break;
                        case "OnLine":
                            DataCommandCmd.Params[1] = $"{_commandMap["OnLine"]}{_hwSubId};{_version};";
                            ISystemBus.SendToHWCmdTopic(JsonSerializer.Serialize(DataCommandCmd), _hwKey);
                            CCSSLogger.Logger.Info($"{CSC} 设备命令字符串:{JsonSerializer.Serialize(DataCommandCmd)}");
                            break;
                        case "OffLine":
                            DataCommandCmd.Params[1] = $"{_commandMap["OffLine"]}{_hwSubId};{_version};";
                            ISystemBus.SendToHWCmdTopic(JsonSerializer.Serialize(DataCommandCmd), _hwKey);
                            CCSSLogger.Logger.Info($"{CSC} 设备命令字符串:{JsonSerializer.Serialize(DataCommandCmd)}");
                            break;
                        default:
                            DataCommandCmd.Params[1] = _commandMap[cmdstr.ToString()];
                            ISystemBus.SendToHWCmdTopic(JsonSerializer.Serialize(DataCommandCmd), _hwKey);
                            CCSSLogger.Logger.Info($"{CSC} 设备命令字符串:{JsonSerializer.Serialize(DataCommandCmd)}");
                            break;
                    }
                    mes = $"{CSC} 设备命令字符串:{JsonSerializer.Serialize(DataCommandCmd)}";
                    RecordLog(mes);
                }
            }
        }
        Finish();
        return true;
    }

    public bool Abort(SubTaskCmdParams Params)
    {
        var status = UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_ABORT_TASK_CMD, _processID!, _subTaskID!);
        ISystemBus.SendToUIStatusTopic(status);
        Logging.CCSSLogger.Logger.Info($"{CSC} 终止: {status}");
        ((ISubTask)this).CleanAllSubs();
        return true;
    }

    public bool Finish(SubTaskCmdParams Params)
    {
        var status = UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_FINISH_TASK_CMD, _processID!, _subTaskID!);
        //通知UI子任务结束
        ISystemBus.SendToUIStatusTopic(status);
        CCSSLogger.Logger.Info($"{CSC} 完成: {status}");
        //通知多任务管理器，子任务结束
        ISystemBus.SendToTaskUpTopic(CmdConsts.SubTaskFinishCmd(Params.ClassName!, Params.ProcessID!, Params.SubTaskID!));
        _unFinishFlag = false;
        ((ISubTask)this).CleanAllSubs();
        return true;
    }

    public bool Finish()
    {
        var status = UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_FINISH_TASK_CMD, _processID!, _subTaskID!);
        //通知UI子任务结束
        ISystemBus.SendToUIStatusTopic(status);
        CCSSLogger.Logger.Info($"{CSC} 完成: {status}");
        //通知多任务管理器，子任务结束
        ISystemBus.SendToTaskUpTopic(CmdConsts.SubTaskFinishCmd(_className!, _processID!, _subTaskID!));
        _unFinishFlag = false;
        ((ISubTask)this).CleanAllSubs();
        return true;
    }

    public bool Resume(SubTaskCmdParams Params)
    {
        if (_unFinishFlag)
        {
            var status = UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_FINISH_TASK_CMD, _processID!, _subTaskID!);
            ISystemBus.SendToUIStatusTopic(status);
            CCSSLogger.Logger.Info($"{CSC} 恢复：{status}");
        }
        return true;
    }

    public bool Pause(SubTaskCmdParams Params)
    {
        if (_unFinishFlag)
        {
            var status = UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_PAUSE_TASK_CMD, _processID!, _subTaskID!);
            ISystemBus.SendToUIStatusTopic(status);
            CCSSLogger.Logger.Info($"{CSC} 暂停: {status}");
        }
        return true;
    }

    public string[] GetSelfTopic()
    {
        CCSSLogger.Logger.Info(_selfTopics.ToArray());
        return _selfTopics.ToArray();
    }

    public void HandleMsgFromScript(string ParamatersString)
    {
        throw new NotImplementedException();
    }

    public void HandleMsgFromUI(string ParamatersString)
    {
        throw new NotImplementedException();
    }

    public void HandleMsgFromVAR(string topic, string ParamatersString)
    {
        throw new NotImplementedException();
    }

    public void ImportHwFuncRet(string ParamatersString)
    {
        CCSSLogger.Logger.Error("字符串命令dll返回值：" + ParamatersString);
    }

    public bool ProcessData(SubTaskCmdParams Params)
    {
        throw new NotImplementedException();
    }

    public JsonElement UIParams()
    {
        throw new NotImplementedException();
    }

    public bool ReStart(SubTaskCmdParams Params)
    {
        throw new NotImplementedException();
    }

    public bool Error(SubTaskCmdParams Params)
    {
        throw new NotImplementedException();
    }
    public void HandleNotify(string notifyTitle, string msg)
    {
        throw new NotImplementedException();
    }
     //记录日志
    public void RecordLog(String? mes)
    {
        ScriptEngine.InstantiatedTemplate.LogControl.LogControl.RecordLog(_templateInst, new RecordLogData("中", "子任务", "Info", mes));
    }
}